import { useState, useRef } from 'react';
import type { ChangeEvent } from 'react';
import { BaseContentPage } from './base-content-page';
import { Label } from '@/components/atoms/label';
import { Upload } from 'lucide-react';
import ContentService from '@/services/content.service';

const AnalyzeFilePage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [provider, setProvider] = useState<'chatgpt' | 'gemini'>('chatgpt');
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    } else {
      setSelectedFile(null);
    }
  };

  const handleSubmit = async () => {
    if (!selectedFile) {
      setError('Por favor, selecione um arquivo');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResult('');

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);

      const response = await ContentService.analyzeFile(formData, provider);

      if (response.analysis || response.content) {
        const resultText = response.analysis || response.content;
        setResult(resultText);
      } else if (!response.success && response.message) {
        setError(response.message);
      } else {
        setResult('Nenhum resultado retornado da análise.');
      }
    } catch (err: any) {
      console.error('Error analyzing file:', err);
      const errorMessage =
        err.response?.data?.message ||
        err.message ||
        'Ocorreu um erro ao analisar o arquivo. Por favor, tente novamente.';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <BaseContentPage
      title="Analisar Arquivo"
      description="Faça upload de um arquivo para análise e geração de conteúdo."
      onSubmit={handleSubmit}
      isLoading={isLoading}
      result={result}
      error={error}
    >
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="file">Arquivo</Label>
          <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-gray-300 border-dashed rounded-md">
            <div className="space-y-1 text-center">
              <Upload className="mx-auto h-12 w-12 text-gray-400" />
              <div className="flex text-sm text-gray-600">
                <label
                  htmlFor="file-upload"
                  className="relative cursor-pointer bg-white rounded-md font-medium text-indigo-600 hover:text-indigo-500 focus-within:outline-none focus-within:ring-2 focus-within:ring-offset-2 focus-within:ring-indigo-500"
                >
                  <span>Faça upload de um arquivo</span>
                  <input
                    id="file-upload"
                    name="file-upload"
                    type="file"
                    className="sr-only"
                    onChange={handleFileChange}
                    ref={fileInputRef}
                  />
                </label>
                <p className="pl-1">ou arraste e solte</p>
              </div>
              <p className="text-xs text-gray-500">
                {selectedFile
                  ? selectedFile.name
                  : 'Nenhum arquivo selecionado'}
              </p>
            </div>
          </div>
        </div>

        <div className="space-y-2">
          <Label htmlFor="provider">Provedor de IA</Label>
          <select
            id="provider"
            className="block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500 sm:text-sm"
            value={provider}
            onChange={(e) => {
              const value = e.target.value as 'chatgpt' | 'gemini';
              setProvider(value);
            }}
          >
            <option value="chatgpt">ChatGPT</option>
            <option value="gemini">Gemini</option>
          </select>
        </div>
      </div>
    </BaseContentPage>
  );
};

export default AnalyzeFilePage;
