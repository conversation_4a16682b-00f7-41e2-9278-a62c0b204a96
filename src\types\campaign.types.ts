
export interface Campaign extends CampaignSetupRequest {
  id: string;
  copy: string;
  status: 'pending' | 'processing' | 'completed' | 'failed';
  createdAt: string;
  updatedAt: string;
}

// Using Campaign directly for API responses
export type CampaignResponse = Campaign;

// Using Campaign[] directly for list responses
export type CampaignListResponse = Campaign[];

export interface CampaignSetupRequest {
  name: string;
  ideia: string;
  emotion: string;
  belief: string;
  type: number;
  conscienceLevel: number;
  sophisticationLevel: number;
  companyId?: string;
  productId?: string;
  publicId?: string;
  expertId?: string;
}

export interface Campaign extends CampaignSetupRequest {
  id: string;
  createdAt: string;
  updatedAt: string;
}
