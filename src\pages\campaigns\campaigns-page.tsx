import { API_BASE_URL } from '@/config';
import { useEffect, useState } from 'react';

interface Campaign {
  id: string;
  name: string;
  description?: string;
  status?: string;
  startDate?: string;
  endDate?: string;
  budget?: number;
}

export default function CampaignsPage() {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchCampaigns = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/campaign-setup`);
        const data = await response.json();
        // Assume que a resposta é um array de campanhas
        setCampaigns(Array.isArray(data) ? data : data?.data || []);
      } catch (error) {
        console.error('Erro ao buscar campanhas:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCampaigns();
  }, []);

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Não definida';
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  const getStatusBadge = (status?: string) => {
    if (!status) return null;

    const statusMap: Record<string, { bg: string; text: string }> = {
      active: { bg: 'bg-green-50', text: 'text-green-700' },
      paused: { bg: 'bg-yellow-50', text: 'text-yellow-700' },
      ended: { bg: 'bg-red-50', text: 'text-red-700' },
      draft: { bg: 'bg-gray-50', text: 'text-gray-700' },
    };

    const statusConfig = statusMap[status.toLowerCase()] || {
      bg: 'bg-gray-50',
      text: 'text-gray-700',
    };

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${statusConfig.bg} ${statusConfig.text}`}
      >
        {status.charAt(0).toUpperCase() + status.slice(1).toLowerCase()}
      </span>
    );
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            Carregando campanhas...
          </h1>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 animate-pulse"
              >
                <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Nossas Campanhas
          <span className="ml-3 text-sm font-normal text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
            {campaigns.length} campanhas
          </span>
        </h1>

        {campaigns.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">Nenhuma campanha encontrada</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {campaigns.map((campaign) => (
              <div
                key={campaign.id}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100 overflow-hidden"
              >
                <div className="p-6">
                  <div className="flex justify-between items-start">
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">
                      {campaign.name || 'Campanha sem nome'}
                    </h2>
                    {getStatusBadge(campaign.status)}
                  </div>

                  {campaign.description && (
                    <p className="mt-3 text-gray-600 text-sm">
                      {campaign.description}
                    </p>
                  )}

                  <div className="mt-4 grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-gray-500 font-medium">Início</p>
                      <p>{formatDate(campaign.startDate)}</p>
                    </div>
                    <div>
                      <p className="text-gray-500 font-medium">Término</p>
                      <p>{formatDate(campaign.endDate)}</p>
                    </div>
                  </div>

                  {campaign.budget !== undefined && (
                    <div className="mt-4 pt-3 border-t border-gray-100">
                      <p className="text-sm text-gray-500">Orçamento</p>
                      <p className="text-lg font-semibold text-gray-900">
                        R${' '}
                        {Number(campaign.budget).toLocaleString('pt-BR', {
                          minimumFractionDigits: 2,
                          maximumFractionDigits: 2,
                        })}
                      </p>
                    </div>
                  )}

                  <div className="mt-4 pt-3 border-t border-gray-100">
                    <span className="text-xs text-gray-500">
                      ID: {campaign.id}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
