import { useState, useEffect } from 'react';
import { ExpertService } from '@/services/expert.service';
import type { Expert } from '@/types/expert.types';
import { toast } from '@/components/atoms/toaster';

export function useExperts() {
  const [experts, setExperts] = useState<Expert[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchExperts = async (): Promise<Expert[]> => {
    try {
      setIsLoading(true);
      const data = await ExpertService.getAllExperts();
      setExperts(data);
      return data;
    } catch (err) {
      console.error('Error fetching experts:', err);
      setError(err as Error);
      toast({
        title: 'Erro',
        description: 'Não foi possível carregar os especialistas',
        variant: 'destructive',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchExperts().catch(() => {});
  }, []);

  return {
    experts,
    isLoading,
    error,
    refresh: fetchExperts,
  };
}
