import { ThemeProvider } from './components';
import { Router } from './routes';
import { Toaster as SonnerToaster } from 'sonner';
import { AuthProvider } from './contexts/auth-context';
import { ToastProvider } from '@/components/atoms/toast';

const App = () => {
  return (
    <ThemeProvider defaultTheme="light" storageKey="@hc/color-theme">
      <AuthProvider>
        <ToastProvider>
          <SonnerToaster richColors position="top-right" />
          <Router />
        </ToastProvider>
      </AuthProvider>
    </ThemeProvider>
  );
};

export default App;
