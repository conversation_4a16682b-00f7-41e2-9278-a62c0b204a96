import { apiClient } from './api';
import type { PublicSetupRequest, ApiResponse } from '../types';
import type { Public } from '@/types/public.types';

export class PublicService {
  private static readonly ENDPOINT = '/public-setup';

  static async createPublic(publicData: PublicSetupRequest) {
    try {
      const response = await apiClient.post(this.ENDPOINT, publicData);
      return response.data;
    } catch (error) {
      console.error('Erro ao criar público:', error);
      throw error;
    }
  }

  static async getPublicById(id: string): Promise<Public> {
    try {
      const response = await apiClient.get(`${this.ENDPOINT}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar público com ID ${id}:`, error);
      throw error;
    }
  }

  static async getAllPublics(): Promise<Public[]> {
    try {
      const response = await apiClient.get<ApiResponse<Public[]>>(
        this.ENDPOINT
      );
      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to fetch publics');
      }
      return response.data.data;
    } catch (error) {
      console.error('Erro ao buscar públicos:', error);
      throw error;
    }
  }

  static async updatePublic(
    id: string,
    publicData: Partial<PublicSetupRequest>
  ): Promise<Public> {
    try {
      const response = await apiClient.put(
        `${this.ENDPOINT}/${id}`,
        publicData
      );
      return response.data;
    } catch (error) {
      console.error(`Erro ao atualizar público com ID ${id}:`, error);
      throw error;
    }
  }
}

export default PublicService;
