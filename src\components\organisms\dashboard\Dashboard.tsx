import React, { useEffect, useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../../atoms/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '../../atoms/tabs';
import type { Campaign, Expert, ApiResponse } from '../../../types';
import type { Product as ProductServiceProduct } from '../../../services/product.service';
import {
  CampaignService,
  ExpertService,
  ProductService,
} from '../../../services';

const Dashboard: React.FC = () => {
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [experts, setExperts] = useState<Expert[]>([]);
  const [products, setProducts] = useState<ProductServiceProduct[]>([]);
  const [loading, setLoading] = useState({
    campaigns: true,
    experts: true,
    products: true,
  });
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const campaignsData = await CampaignService.getAllCampaigns(1, 10);
        setCampaigns(campaignsData.items);
        setLoading((prev) => ({ ...prev, campaigns: false }));

        const expertsData = await ExpertService.getAllExperts();
        setExperts(expertsData);
        setLoading((prev) => ({ ...prev, experts: false }));

        const response = (await ProductService.getAllProducts()) as
          | ApiResponse<ProductServiceProduct[]>
          | ProductServiceProduct[];

        const productsData = Array.isArray(response)
          ? response
          : (response as ApiResponse<ProductServiceProduct[]>)?.data || [];
        setProducts(productsData);
        setLoading((prev) => ({ ...prev, products: false }));
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data. Please try again later.');
        setLoading({
          campaigns: false,
          experts: false,
          products: false,
        });
      }
    };

    fetchData();
  }, []);

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (error) {
    return (
      <div className="container mx-auto p-4">
        <div className="text-red-500">{error}</div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-6">Dashboard</h1>

      <Tabs defaultValue="campaigns" className="space-y-4">
        <TabsList>
          <TabsTrigger value="campaigns">Campanhas</TabsTrigger>
          <TabsTrigger value="experts">Especialistas</TabsTrigger>
          <TabsTrigger value="products">Produtos</TabsTrigger>
        </TabsList>

        <TabsContent value="campaigns">
          <Card>
            <CardHeader>
              <CardTitle>Campanhas</CardTitle>
            </CardHeader>
            <CardContent>
              {loading.campaigns ? (
                <div>Carregando campanhas...</div>
              ) : campaigns.length === 0 ? (
                <div>Nenhuma campanha encontrada.</div>
              ) : (
                <div className="space-y-4">
                  {campaigns.map((campaign) => (
                    <div key={campaign.id} className="border p-4 rounded-lg">
                      <h3 className="font-semibold">{campaign.name}</h3>
                      <p className="text-sm text-gray-500">
                        Criado em: {formatDate(campaign.createdAt)}
                      </p>
                      <p className="text-sm">Tipo: {campaign.type}</p>
                      <p className="text-sm">
                        Nível de Consciência: {campaign.conscienceLevel}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="experts">
          <Card>
            <CardHeader>
              <CardTitle>Especialistas</CardTitle>
            </CardHeader>
            <CardContent>
              {loading.experts ? (
                <div>Carregando especialistas...</div>
              ) : experts.length === 0 ? (
                <div>Nenhum especialista encontrado.</div>
              ) : (
                <div className="space-y-4">
                  {experts.map((expert) => (
                    <div key={expert.id} className="border p-4 rounded-lg">
                      <h3 className="font-semibold">{expert.name}</h3>
                      <p className="text-sm text-gray-500">
                        Criado em: {formatDate(expert.createdAt)}
                      </p>
                      <p className="text-sm">Área: {expert.areaOfExpertise}</p>
                      <p className="text-sm">Empresa: {expert.enterprise}</p>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="products">
          <Card>
            <CardHeader>
              <CardTitle>Produtos</CardTitle>
            </CardHeader>
            <CardContent>
              {loading.products ? (
                <div>Carregando produtos...</div>
              ) : products.length === 0 ? (
                <div>Nenhum produto encontrado.</div>
              ) : (
                <div className="space-y-4">
                  {products.map((product) => (
                    <div key={product.id} className="border p-4 rounded-lg">
                      <h3 className="font-semibold">{product.name}</h3>
                      {product.createdAt && (
                        <p className="text-sm text-gray-500">
                          Criado em: {formatDate(product.createdAt)}
                        </p>
                      )}
                      {product.description && (
                        <p className="text-sm">
                          Descrição: {product.description}
                        </p>
                      )}
                      {product.price && (
                        <p className="text-sm">
                          Preço:{' '}
                          {new Intl.NumberFormat('pt-BR', {
                            style: 'currency',
                            currency: 'BRL',
                          }).format(product.price)}
                        </p>
                      )}
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Dashboard;
