# Use Node.js official image
FROM node:20

# Set working directory
WORKDIR /app

# Install build tools for native modules
RUN apt-get update && apt-get install -y python3 make g++ && rm -rf /var/lib/apt/lists/*

# Copy package files
COPY package*.json ./

# Install dependencies
RUN npm install --legacy-peer-deps
RUN npm install @rollup/rollup-linux-x64-gnu --legacy-peer-deps
RUN npm install lightningcss-linux-x64-gnu --legacy-peer-deps

# Rebuild all native modules for the current platform
RUN npm rebuild

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Install serve to run the app
RUN npm install -g serve

# Expose port
EXPOSE 3000

# Start the application
CMD ["serve", "-s", "dist", "-l", "3000"]
