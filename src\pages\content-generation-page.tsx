import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from '@/components/atoms/card';
import {
  Ta<PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Ta<PERSON>List,
  TabsTrigger,
} from '@/components/atoms/tabs';
import { Button } from '@/components/atoms/button';
import { Input } from '@/components/atoms/input';
import { Label } from '@/components/atoms/label';
import { Textarea } from '@/components/atoms/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/atoms/select';
import { Loader2 } from 'lucide-react';
import {
  type GenerateContentRequest,
  type AnalyzeWebsiteRequest,
} from '@/services/content.service';
import {
  CopyService,
  type GenerateCompleteCopyRequest,
} from '@/services/copy.service';
import ContentService from '@/services/content.service';

const ContentGenerationPage: React.FC = () => {
  const [activeTab, setActiveTab] = useState('generate');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [result, setResult] = useState('');
  const [provider, setProvider] = useState('chatgpt');

  const [prompt, setPrompt] = useState('');
  const [copyRequest, setCopyRequest] = useState({
    campaignId: '',
    expertId: '',
    productId: '',
    publicId: '',
    request: '',
  });
  const [websiteUrl, setWebsiteUrl] = useState('');
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  const handleGenerateContent = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!prompt.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      const data: GenerateContentRequest = {
        prompt,
        preferredProvider: provider as any,
      };

      const response = await ContentService.generateContent(data);
      setResult(response.content);
    } catch (err) {
      setError('Failed to generate content. Please try again.');
      console.error('Error generating content:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleGenerateCompleteCopy = async (e: React.FormEvent) => {
    e.preventDefault();
    if (
      !copyRequest.campaignId ||
      !copyRequest.expertId ||
      !copyRequest.productId ||
      !copyRequest.publicId ||
      !copyRequest.request
    ) {
      setError('Por favor, preencha todos os campos obrigatórios');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const requestData: GenerateCompleteCopyRequest = {
        campaignId: copyRequest.campaignId,
        expertId: copyRequest.expertId,
        productId: copyRequest.productId,
        publicId: copyRequest.publicId,
        copyRequest: copyRequest.request,
        provider: provider as 'chatgpt' | 'gemini',
      };

      const response = await CopyService.generateCompleteCopy(requestData);
      setResult(response.generatedCopy);
    } catch (err) {
      setError('Falha ao gerar o copy. Por favor, tente novamente.');
      console.error('Erro ao gerar copy:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleAnalyzeWebsite = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!websiteUrl.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      const data: AnalyzeWebsiteRequest = {
        url: websiteUrl,
        provider: provider as any,
      };

      const response = await ContentService.analyzeWebsite(data);
      setResult(response.content);
    } catch (err) {
      setError('Failed to analyze website. Please try again.');
      console.error('Error analyzing website:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleAnalyzeFile = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedFile) return;

    setIsLoading(true);
    setError(null);

    try {
      const formData = new FormData();
      formData.append('file', selectedFile);
      formData.append('provider', provider);

      const response = await ContentService.analyzeFile(formData, provider);
      setResult(response.content);
    } catch (err) {
      setError('Falha ao analisar o arquivo. Por favor, tente novamente.');
      console.error('Erro ao analisar arquivo:', err);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-6">Content Generation</h1>

      <Tabs
        value={activeTab}
        onValueChange={setActiveTab}
        className="space-y-4"
      >
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="generate">Generate Content</TabsTrigger>
          <TabsTrigger value="copy">Complete Copy</TabsTrigger>
          <TabsTrigger value="website">Analyze Website</TabsTrigger>
          <TabsTrigger value="file">Analyze File</TabsTrigger>
        </TabsList>

        <TabsContent value="generate">
          <Card>
            <CardHeader>
              <CardTitle>Generate Content</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleGenerateContent} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="prompt">Enter your prompt</Label>
                  <Textarea
                    id="prompt"
                    value={prompt}
                    onChange={(e) => setPrompt(e.target.value)}
                    placeholder="Enter your content generation prompt..."
                    rows={4}
                    required
                  />
                </div>

                <div className="flex items-center space-x-4">
                  <Label htmlFor="provider">AI Provider</Label>
                  <Select value={provider} onValueChange={setProvider}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select provider" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="chatgpt">ChatGPT</SelectItem>
                      <SelectItem value="gemini">Gemini</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button type="submit" disabled={isLoading || !prompt.trim()}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    'Generate Content'
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="copy">
          <Card>
            <CardHeader>
              <CardTitle>Generate Complete Copy</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleGenerateCompleteCopy} className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="campaignId">Campaign ID</Label>
                    <Input
                      id="campaignId"
                      value={copyRequest.campaignId}
                      onChange={(e) =>
                        setCopyRequest({
                          ...copyRequest,
                          campaignId: e.target.value,
                        })
                      }
                      placeholder="Enter campaign ID"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="expertId">Expert ID</Label>
                    <Input
                      id="expertId"
                      value={copyRequest.expertId}
                      onChange={(e) =>
                        setCopyRequest({
                          ...copyRequest,
                          expertId: e.target.value,
                        })
                      }
                      placeholder="Enter expert ID"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="productId">Product ID</Label>
                    <Input
                      id="productId"
                      value={copyRequest.productId}
                      onChange={(e) =>
                        setCopyRequest({
                          ...copyRequest,
                          productId: e.target.value,
                        })
                      }
                      placeholder="Enter product ID"
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="publicId">Public ID</Label>
                    <Input
                      id="publicId"
                      value={copyRequest.publicId}
                      onChange={(e) =>
                        setCopyRequest({
                          ...copyRequest,
                          publicId: e.target.value,
                        })
                      }
                      placeholder="Enter public ID"
                      required
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="copyRequest">Copy Request</Label>
                  <Textarea
                    id="copyRequest"
                    value={copyRequest.request}
                    onChange={(e) =>
                      setCopyRequest({
                        ...copyRequest,
                        request: e.target.value,
                      })
                    }
                    placeholder="Enter your copy request details..."
                    rows={4}
                    required
                  />
                </div>

                <div className="flex items-center space-x-4">
                  <Label htmlFor="provider-copy">AI Provider</Label>
                  <Select value={provider} onValueChange={setProvider}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select provider" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="chatgpt">ChatGPT</SelectItem>
                      <SelectItem value="gemini">Gemini</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button
                  type="submit"
                  disabled={isLoading || !copyRequest.request.trim()}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Generating Copy...
                    </>
                  ) : (
                    'Generate Complete Copy'
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="website">
          <Card>
            <CardHeader>
              <CardTitle>Analyze Website</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleAnalyzeWebsite} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="websiteUrl">Website URL</Label>
                  <Input
                    id="websiteUrl"
                    type="url"
                    value={websiteUrl}
                    onChange={(e) => setWebsiteUrl(e.target.value)}
                    placeholder="https://example.com"
                    required
                  />
                </div>

                <div className="flex items-center space-x-4">
                  <Label htmlFor="provider-website">AI Provider</Label>
                  <Select value={provider} onValueChange={setProvider}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select provider" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="chatgpt">ChatGPT</SelectItem>
                      <SelectItem value="gemini">Gemini</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button
                  type="submit"
                  disabled={isLoading || !websiteUrl.trim()}
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    'Analyze Website'
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="file">
          <Card>
            <CardHeader>
              <CardTitle>Analyze File</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleAnalyzeFile} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="file">Upload File</Label>
                  <Input
                    id="file"
                    type="file"
                    onChange={handleFileChange}
                    accept=".txt,.pdf,.doc,.docx"
                    required
                  />
                  {selectedFile && (
                    <p className="text-sm text-muted-foreground">
                      Selected file: {selectedFile.name} (
                      {(selectedFile.size / 1024).toFixed(2)} KB)
                    </p>
                  )}
                </div>

                <div className="flex items-center space-x-4">
                  <Label htmlFor="provider-file">AI Provider</Label>
                  <Select value={provider} onValueChange={setProvider}>
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Select provider" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="chatgpt">ChatGPT</SelectItem>
                      <SelectItem value="gemini">Gemini</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button type="submit" disabled={isLoading || !selectedFile}>
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Analyzing...
                    </>
                  ) : (
                    'Analyze File'
                  )}
                </Button>
              </form>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {error && (
        <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-md">
          <p className="text-red-600">{error}</p>
        </div>
      )}

      {result && (
        <div className="mt-8">
          <h2 className="text-xl font-semibold mb-4">Result</h2>
          <div className="p-4 bg-muted rounded-md whitespace-pre-wrap">
            {result}
          </div>
          <div className="mt-2 text-sm text-muted-foreground">
            Generated with {provider === 'chatgpt' ? 'ChatGPT' : 'Gemini'}
          </div>
        </div>
      )}
    </div>
  );
};

export default ContentGenerationPage;
