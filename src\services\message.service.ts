import { apiClient } from './api';

export interface Copy {
  id: string;
  title?: string;
  content?: string;
  userPrompt?: string;
  aiResponse?: string;
  provider?: string;
  messageType?: string;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt?: string;
}

export class MessageService {
  private static readonly BASE_ENDPOINT = '/messages';

  static async getAll(): Promise<Copy[]> {
    const response = await apiClient.get<Copy[]>(this.BASE_ENDPOINT);
    return response.data;
  }

  static async getById(id: string): Promise<Copy> {
    const response = await apiClient.get<Copy>(`${this.BASE_ENDPOINT}/${id}`);
    return response.data;
  }
}
