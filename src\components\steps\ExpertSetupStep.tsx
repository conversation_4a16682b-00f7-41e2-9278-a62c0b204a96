import React from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '../atoms/button';
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../atoms/card';
import { Input } from '../atoms/input';
import { Label } from '../atoms/label';
import { Textarea } from '../atoms/textarea';
import { ExpertService } from '@/services';

interface ExpertFormData {
  name: string;
  areaOfExpertise: string;
  biography: string;
  moment: string;
  dolor: string;
  credibility: string;
  recognition: string;
  trackRecord: string;
  howProductWorks: string;
  voicePersonality: string;
  essentialValues: string;
  enterprise: string;
}

interface ExpertSetupStepProps {
  onSubmit?: (data: ExpertFormData) => void;
  onNext?: () => void;
  onSuccess?: () => void;
  initialData?: Partial<ExpertFormData>;
}

const ExpertSetupStep: React.FC<ExpertSetupStepProps> = ({
  onSubmit,
  onNext,
  onSuccess,
  initialData = {},
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ExpertFormData>({
    defaultValues: {
      name: '',
      areaOfExpertise: '',
      biography: '',
      moment: '',
      dolor: '',
      credibility: '',
      recognition: '',
      trackRecord: '',
      howProductWorks: '',
      voicePersonality: '',
      essentialValues: '',
      enterprise: '',
      ...initialData,
    },
  });

  const handleFormSubmit = async (data: ExpertFormData) => {
    try {
      if (onSubmit) {
        await onSubmit(data);
      } else {
        await ExpertService.createExpert(data);
      }
      if (onNext) {
        onNext();
      } else if (onSuccess) {
        onSuccess();
      }
    } catch (error) {
      console.error('Error submitting expert data:', error);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Configuração do Expert</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="name">Nome do Expert</Label>
            <Input
              id="name"
              {...register('name', { required: 'Campo obrigatório' })}
              placeholder="Nome do especialista ou autoridade"
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="areaOfExpertise">Área de Atuação</Label>
            <Input
              id="areaOfExpertise"
              {...register('areaOfExpertise', {
                required: 'Campo obrigatório',
              })}
              placeholder="Ex: Marketing Digital, Desenvolvimento Web..."
            />
            {errors.areaOfExpertise && (
              <p className="text-sm text-red-500">
                {errors.areaOfExpertise.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="biography">Biografia</Label>
            <Textarea
              id="biography"
              {...register('biography', { required: 'Campo obrigatório' })}
              placeholder="Digite a biografia completa"
              className="min-h-[100px]"
            />
            {errors.biography && (
              <p className="text-sm text-red-500">{errors.biography.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="moment">Momento do Expert</Label>
            <Textarea
              id="moment"
              {...register('moment', { required: 'Campo obrigatório' })}
              placeholder="Momento atual do expert"
              className="min-h-[60px]"
            />
            {errors.moment && (
              <p className="text-sm text-red-500">{errors.moment.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="dolor">Dor do Expert</Label>
            <Textarea
              id="dolor"
              {...register('dolor', { required: 'Campo obrigatório' })}
              placeholder="Descreva as dores que o expert ajuda a resolver"
              className="min-h-[60px]"
            />
            {errors.dolor && (
              <p className="text-sm text-red-500">{errors.dolor.message}</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="credibility">Credibilidade</Label>
            <Textarea
              id="credibility"
              {...register('credibility', { required: 'Campo obrigatório' })}
              placeholder="Fatores que comprovam a credibilidade"
              className="min-h-[60px]"
            />
            {errors.credibility && (
              <p className="text-sm text-red-500">
                {errors.credibility.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="recognition">Reconhecimento</Label>
            <Textarea
              id="recognition"
              {...register('recognition', { required: 'Campo obrigatório' })}
              placeholder="Reconhecimentos e prêmios recebidos"
              className="min-h-[60px]"
            />
            {errors.recognition && (
              <p className="text-sm text-red-500">
                {errors.recognition.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="trackRecord">Histórico</Label>
            <Textarea
              id="trackRecord"
              {...register('trackRecord', { required: 'Campo obrigatório' })}
              placeholder="Histórico profissional e conquistas"
              className="min-h-[60px]"
            />
            {errors.trackRecord && (
              <p className="text-sm text-red-500">
                {errors.trackRecord.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="howProductWorks">Como o Produto Funciona</Label>
            <Textarea
              id="howProductWorks"
              {...register('howProductWorks', {
                required: 'Campo obrigatório',
              })}
              placeholder="Explicação de como o produto/serviço funciona"
              className="min-h-[60px]"
            />
            {errors.howProductWorks && (
              <p className="text-sm text-red-500">
                {errors.howProductWorks.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="voicePersonality">Personalidade da Voz</Label>
            <Textarea
              id="voicePersonality"
              {...register('voicePersonality', {
                required: 'Campo obrigatório',
              })}
              placeholder="Como é a personalidade da voz do expert"
              className="min-h-[60px]"
            />
            {errors.voicePersonality && (
              <p className="text-sm text-red-500">
                {errors.voicePersonality.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="essentialValues">Valores Essenciais</Label>
            <Textarea
              id="essentialValues"
              {...register('essentialValues', {
                required: 'Campo obrigatório',
              })}
              placeholder="Quais são os valores essenciais do expert"
              className="min-h-[60px]"
            />
            {errors.essentialValues && (
              <p className="text-sm text-red-500">
                {errors.essentialValues.message}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="enterprise">Empresa</Label>
            <Input
              id="enterprise"
              {...register('enterprise', { required: 'Campo obrigatório' })}
              placeholder="Nome da empresa"
            />
            {errors.enterprise && (
              <p className="text-sm text-red-500">
                {errors.enterprise.message}
              </p>
            )}
          </div>

          <div className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => window.history.back()}
            >
              Voltar
            </Button>
            <Button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Salvar e Continuar
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default ExpertSetupStep;
