# 🚀 Configuração de Deploy Automático para Google Cloud Storage

Este guia explica como configurar o deploy automático do seu projeto React para o Google Cloud Storage usando GitHub Actions com **Workload Identity Provider** (método mais seguro).

## 📋 Pré-requisitos

1. **Conta no Google Cloud Platform**
2. **Projeto no GCP criado**
3. **Repositório no GitHub**
4. **Buckets do Cloud Storage criados**

## 🔧 Configuração do Google Cloud Platform

### 1. Habilitar APIs Necessárias

```bash
# Fazer login no GCP
gcloud auth login

# Definir o projeto
gcloud config set project SEU_PROJECT_ID

# Habilitar APIs necessárias
gcloud services enable iamcredentials.googleapis.com
gcloud services enable storage.googleapis.com
gcloud services enable sts.googleapis.com
```

### 2. Criar Service Account

```bash
# Criar service account
gcloud iam service-accounts create github-actions \
    --description="Service account for GitHub Actions with Workload Identity" \
    --display-name="GitHub Actions Workload Identity"

# Obter o email da service account
SERVICE_ACCOUNT_EMAIL="github-actions@SEU_PROJECT_ID.iam.gserviceaccount.com"
echo "Service Account: $SERVICE_ACCOUNT_EMAIL"
```

### 3. Conceder Permissões à Service Account

```bash
# Conceder permissões de Storage Admin
gcloud projects add-iam-policy-binding SEU_PROJECT_ID \
    --member="serviceAccount:github-actions@SEU_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.admin"

# Conceder permissões de Storage Object Admin
gcloud projects add-iam-policy-binding SEU_PROJECT_ID \
    --member="serviceAccount:github-actions@SEU_PROJECT_ID.iam.gserviceaccount.com" \
    --role="roles/storage.objectAdmin"
```

### 4. Criar Workload Identity Pool

```bash
# Criar Workload Identity Pool
gcloud iam workload-identity-pools create "github-pool" \
    --project="SEU_PROJECT_ID" \
    --location="global" \
    --display-name="GitHub Actions Pool"

# Obter o nome completo do pool
WORKLOAD_IDENTITY_POOL_ID="projects/$(gcloud config get-value project)/locations/global/workloadIdentityPools/github-pool"
echo "Pool ID: $WORKLOAD_IDENTITY_POOL_ID"
```

### 5. Criar Workload Identity Provider

```bash
# Criar provider para GitHub Actions
gcloud iam workload-identity-pools providers create-oidc "github-provider" \
    --project="SEU_PROJECT_ID" \
    --location="global" \
    --workload-identity-pool="github-pool" \
    --display-name="GitHub Actions Provider" \
    --attribute-mapping="google.subject=assertion.sub,attribute.actor=assertion.actor,attribute.repository=assertion.repository,attribute.repository_owner=assertion.repository_owner" \
    --issuer-uri="https://token.actions.githubusercontent.com"

# Obter o nome completo do provider
WORKLOAD_IDENTITY_PROVIDER="projects/$(gcloud config get-value project)/locations/global/workloadIdentityPools/github-pool/providers/github-provider"
echo "Provider: $WORKLOAD_IDENTITY_PROVIDER"
```

### 6. Configurar Binding da Service Account

```bash
# Permitir que o repositório GitHub use a service account
# Substitua OWNER/REPO pelo seu repositório (ex: meuusuario/meu-projeto)
gcloud iam service-accounts add-iam-policy-binding \
    --role roles/iam.workloadIdentityUser \
    --member "principalSet://iam.googleapis.com/${WORKLOAD_IDENTITY_POOL_ID}/attribute.repository/OWNER/REPO" \
    github-actions@SEU_PROJECT_ID.iam.gserviceaccount.com

# Para permitir apenas a branch main (mais seguro)
gcloud iam service-accounts add-iam-policy-binding \
    --role roles/iam.workloadIdentityUser \
    --member "principalSet://iam.googleapis.com/${WORKLOAD_IDENTITY_POOL_ID}/attribute.repository/OWNER/REPO" \
    --condition='expression=assertion.ref=="refs/heads/main"' \
    github-actions@SEU_PROJECT_ID.iam.gserviceaccount.com
```

### 4. Criar Buckets do Cloud Storage

```bash
# Bucket para produção
gsutil mb -p SEU_PROJECT_ID -c STANDARD -l us-central1 gs://SEU_BUCKET_PRODUCAO

# Bucket para preview (opcional)
gsutil mb -p SEU_PROJECT_ID -c STANDARD -l us-central1 gs://SEU_BUCKET_PREVIEW

# Configurar buckets para website estático
gsutil web set -m index.html -e 404.html gs://SEU_BUCKET_PRODUCAO
gsutil web set -m index.html -e 404.html gs://SEU_BUCKET_PREVIEW

# Tornar buckets públicos
gsutil iam ch allUsers:objectViewer gs://SEU_BUCKET_PRODUCAO
gsutil iam ch allUsers:objectViewer gs://SEU_BUCKET_PREVIEW
```

## 🔐 Configuração dos GitHub Secrets

Vá para **Settings > Secrets and variables > Actions** no seu repositório GitHub e adicione:

### Secrets Obrigatórios:

| Nome                  | Valor                                                                                       | Descrição                  |
| --------------------- | ------------------------------------------------------------------------------------------- | -------------------------- |
| `GCP_PROJECT_ID`      | `seu-project-id`                                                                            | ID do projeto no GCP       |
| `GCS_BUCKET_NAME`     | `seu-bucket-producao`                                                                       | Nome do bucket de produção |
| `WIF_PROVIDER`        | `projects/123/locations/global/workloadIdentityPools/github-pool/providers/github-provider` | Workload Identity Provider |
| `WIF_SERVICE_ACCOUNT` | `<EMAIL>`                                     | Email da service account   |

### Secrets Opcionais (para preview):

| Nome                      | Valor                | Descrição                 |
| ------------------------- | -------------------- | ------------------------- |
| `GCS_PREVIEW_BUCKET_NAME` | `seu-bucket-preview` | Nome do bucket de preview |

### ⚠️ Importante sobre Workload Identity

- **Não é necessário** criar ou armazenar chaves JSON
- **Mais seguro** que chaves estáticas
- **Rotação automática** de credenciais
- **Auditoria completa** de acesso

## 📁 Estrutura dos Workflows

### 1. Deploy Principal (`.github/workflows/deploy.yml`)

- **Trigger**: Push na branch `main`
- **Ações**:
  - Build do projeto
  - Deploy para bucket de produção
  - Configuração de cache headers
  - Configuração de website estático

### 2. Preview Deploy (`.github/workflows/preview.yml`)

- **Trigger**: Pull Request para `main`
- **Ações**:
  - Build do projeto
  - Deploy para bucket de preview
  - Comentário no PR com URL de preview
  - Cleanup automático quando PR é fechado

## 🌐 URLs de Acesso

### Produção

```
https://storage.googleapis.com/SEU_BUCKET_PRODUCAO/index.html
```

### Preview (Pull Requests)

```
https://storage.googleapis.com/SEU_BUCKET_PREVIEW/pr-NUMERO_DO_PR/index.html
```

## 🎯 Domínio Customizado (Opcional)

### 1. Configurar CNAME

```bash
# Verificar propriedade do domínio
gsutil web set -m index.html -e 404.html gs://SEU_DOMINIO.com

# Criar CNAME no seu DNS
# CNAME: www -> c.storage.googleapis.com
```

### 2. Certificado SSL

Para HTTPS, use o Cloud Load Balancer ou Cloudflare.

## 🔍 Monitoramento e Logs

### Ver logs do deploy:

1. Vá para **Actions** no GitHub
2. Clique no workflow executado
3. Veja os logs detalhados de cada step

### Verificar arquivos no bucket:

```bash
gsutil ls -la gs://SEU_BUCKET_PRODUCAO/
```

## 🛠️ Troubleshooting

### Erro de Permissões

```bash
# Verificar permissões da service account
gcloud projects get-iam-policy SEU_PROJECT_ID \
    --flatten="bindings[].members" \
    --format="table(bindings.role)" \
    --filter="bindings.members:github-actions@SEU_PROJECT_ID.iam.gserviceaccount.com"
```

### Erro de Build

- Verifique se todas as dependências estão no `package.json`
- Confirme se o comando `npm run build` funciona localmente
- Verifique se não há erros de lint

### Bucket não acessível

```bash
# Verificar se bucket é público
gsutil iam get gs://SEU_BUCKET_PRODUCAO

# Tornar público se necessário
gsutil iam ch allUsers:objectViewer gs://SEU_BUCKET_PRODUCAO
```

## 📊 Otimizações

### Cache Headers

- **Assets estáticos** (JS, CSS, imagens): 1 ano
- **HTML**: Sem cache (para atualizações imediatas)

### Compressão

Os arquivos são automaticamente comprimidos pelo Vite durante o build.

### CDN

Para melhor performance global, considere usar o Cloud CDN do GCP.

## 🔄 Atualizações

Para atualizar o workflow:

1. Edite os arquivos `.github/workflows/*.yml`
2. Commit e push para `main`
3. O novo workflow será aplicado automaticamente

## 📞 Suporte

Em caso de problemas:

1. Verifique os logs do GitHub Actions
2. Confirme as permissões no GCP
3. Teste o build localmente
4. Verifique se todos os secrets estão configurados
