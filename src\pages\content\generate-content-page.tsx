import React, { useState } from 'react';
import { BaseContentPage } from './base-content-page';
import { Label } from '@/components/atoms/label';
import { Textarea } from '@/components/atoms/textarea';
import ContentService from '@/services/content.service';

const GenerateContentPage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [prompt, setPrompt] = useState('');
  const [provider, setProvider] = useState<'chatgpt' | 'gemini'>('chatgpt');

  const handleSubmit = async () => {
    if (!prompt.trim()) {
      setError('Por favor, insira um prompt válido');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResult('');

    try {
      const response = await ContentService.generateContent({
        prompt,
        preferredProvider: provider,
      });

      if (response.content) {
        setResult(response.content);
      } else {
        setResult(
          'Nenhum conteúdo foi gerado. Por favor, tente novamente com um prompt diferente.'
        );
      }
    } catch (err: any) {
      console.error('Error generating content:', err);
      const errorMessage =
        err.response?.data?.message ||
        err.message ||
        'Ocorreu um erro ao gerar o conteúdo. Por favor, tente novamente.';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <BaseContentPage
      title="Gerar Conteúdo"
      description="Crie conteúdo criativo com base em um prompt."
      onSubmit={handleSubmit}
      isLoading={isLoading}
      result={result}
      error={error}
    >
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="prompt">Prompt</Label>
          <Textarea
            id="prompt"
            placeholder="Descreva como podemos te ajudar a gerar conteúdo para o seu produto"
            value={prompt}
            onChange={(e) => setPrompt(e.target.value)}
            rows={5}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="provider">Provedor</Label>
          <select
            id="provider"
            value={provider}
            onChange={(e) => {
              const value = e.target.value as 'chatgpt' | 'gemini';
              setProvider(value);
            }}
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          >
            <option value="chatgpt">ChatGPT</option>
            <option value="gemini">Gemini</option>
          </select>
        </div>
      </div>
    </BaseContentPage>
  );
};

export default GenerateContentPage;
