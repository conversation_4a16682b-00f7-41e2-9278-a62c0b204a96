import { useState } from 'react';
import { BaseContentPage } from './base-content-page';
import { Input } from '@/components/atoms/input';
import { Label } from '@/components/atoms/label';
import ContentService from '@/services/content.service';

const AnalyzeWebsitePage: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [result, setResult] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [url, setUrl] = useState('');
  const [provider, setProvider] = useState<'chatgpt' | 'gemini'>('chatgpt');

  const handleSubmit = async () => {
    if (!url.trim()) {
      setError('Por favor, insira uma URL válida');
      return;
    }

    setIsLoading(true);
    setError(null);
    setResult('');

    try {
      const response = await ContentService.analyzeWebsite({
        url,
        provider,
      });

      if (response.analysis || response.content) {
        const resultText = response.analysis || response.content;
        setResult(resultText);
      } else if (!response.success && response.message) {
        setError(response.message);
      } else {
        setResult('Nenhum resultado retornado da análise do website.');
      }
    } catch (err: any) {
      console.error('Error analyzing website:', err);
      const errorMessage =
        err.response?.data?.message ||
        err.message ||
        'Ocorreu um erro ao analisar o website. Por favor, verifique a URL e tente novamente.';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <BaseContentPage
      title="Analisar Website"
      description="Analise um site e gere um copy otimizado com base no conteúdo encontrado."
      onSubmit={handleSubmit}
      isLoading={isLoading}
      result={result}
      error={error}
    >
      <div className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor="url">URL do Website</Label>
          <Input
            id="url"
            type="url"
            placeholder="https://exemplo.com"
            value={url}
            onChange={(e) => setUrl(e.target.value)}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor="provider">Provedor</Label>
          <select
            id="provider"
            value={provider}
            onChange={(e) => {
              const value = e.target.value as 'chatgpt' | 'gemini';
              setProvider(value);
            }}
            className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
          >
            <option value="chatgpt">ChatGPT</option>
            <option value="gemini">Gemini</option>
          </select>
        </div>
      </div>
    </BaseContentPage>
  );
};

export default AnalyzeWebsitePage;
