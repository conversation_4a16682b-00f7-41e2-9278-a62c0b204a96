type EnumType = { [key: string]: string | number };

export function getEnumEntries<T extends EnumType>(
  enumObj: T
): Array<[string, T[keyof T]]> {
  return Object.entries(enumObj)
    .filter(([key]) => isNaN(Number(key)))
    .map(([key, value]) => [key, value as T[keyof T]]);
}

export function getEnumValues<T extends EnumType>(
  enumObj: T
): Array<T[keyof T]> {
  return Object.values(enumObj).filter(
    (value): value is T[keyof T] => 
      typeof value === 'string' || typeof value === 'number'
  );
}

export function getEnumKeys<T extends EnumType>(
  enumObj: T
): Array<keyof T> {
  return Object.entries(enumObj)
    .filter(([_, value]) => typeof value === 'string' || typeof value === 'number')
    .map(([key]) => key as keyof T);
}
