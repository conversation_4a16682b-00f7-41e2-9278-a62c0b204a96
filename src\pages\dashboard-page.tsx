import { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON>onte<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from '@/components/atoms/card';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>ist,
  Ta<PERSON>Trigger,
} from '@/components/atoms/tabs';
import { ProductService } from '@/services/product.service';
import { PublicService } from '@/services/public.service';
import { ExpertService } from '@/services/expert.service';
import { CampaignService } from '@/services/campaign.service';
import ProductSetupStep from '@/components/steps/ProductSetupStep';
import PublicSetupStep from '@/components/steps/PublicSetupStep';
import ExpertSetupStep from '@/components/steps/ExpertSetupStep';
import CampaignSetupStep from '@/components/steps/CampaignSetupStep';

type StepType = 'product' | 'public' | 'expert' | 'campaign';

interface CampaignData {
  id?: string;
  name?: string;
  description?: string;
  price?: number;
  deliverables?: number;
  customerJourney?: number;
  ageGroup?: number;
  gender?: string;
  educationLevel?: string;
  interests?: string[];
  expertName?: string;
  expertise?: string;
  campaignName?: string;
  campaignType?: string;
  productId?: string;
  publicId?: string;
  expertId?: string;
  campaignId?: string;
  [key: string]: any;
}

const steps: StepType[] = ['product', 'public', 'expert', 'campaign'];

const DashboardPage: React.FC = (): React.ReactElement => {
  const [activeTab, setActiveTab] = useState<StepType>('product');
  const [completedSteps, setCompletedSteps] = useState<StepType[]>([]);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [extendedCampaignData, setExtendedCampaignData] = useState<CampaignData>({});

  const isStepCompleted = (step: StepType) => {
    return completedSteps.includes(step);
  };

  const handleTabChange = (tab: string) => {
    const tabAsStep = tab as StepType;
    setActiveTab(tabAsStep);
    // Rola para o topo ao mudar de aba
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const markStepAsCompleted = (step: StepType) => {
    if (!completedSteps.includes(step)) {
      setCompletedSteps([...completedSteps, step]);
    }
  };

  const goToNextStep = (currentTab: StepType = activeTab) => {
    const currentIndex = steps.indexOf(currentTab);
    if (currentIndex < steps.length - 1) {
      const nextTab = steps[currentIndex + 1];
      setActiveTab(nextTab);
      // Scroll to top of the form when changing steps
      window.scrollTo({ top: 0, behavior: 'smooth' });
    }
  };

  const updateCampaignData = (data: Partial<CampaignData>) => {
    setExtendedCampaignData(prev => ({
      ...prev,
      ...data
    }));
  };

  const handleStepComplete = async (formData: any = {}) => {
    if (isSubmitting) return;

    try {
      setIsSubmitting(true);
      let response;
      let shouldGoToNextStep = false;

      switch (activeTab) {
        case 'product': {
          const productData = {
            name: formData.name || '',
            benefits: formData.benefits || '',
            socialProof: formData.socialProof || '',
            metodology: formData.metodology || '',
            guarantee: formData.guarantee || '',
            deliverables: Number(formData.deliverables) || 0,
            customerJourney: Number(formData.customerJourney) || 0,
          };
          response = await ProductService.createProduct(productData);
          if (response?.id) {
            updateCampaignData({ ...productData, productId: response.id });
            markStepAsCompleted('product');
            shouldGoToNextStep = true;
          }
          break;
        }

        case 'public': {
          const publicData = {
            name: formData.name || 'Default Name',
            local: formData.local || 'Default Local',
            familySituation: formData.familySituation || 'Default Family Situation',
            personality: formData.personality || 'Default Personality',
            hobbies: formData.hobbies || 'Default Hobbies',
            lifestyle: formData.lifestyle || 'Default Lifestyle',
            personalValue: formData.personalValue || 'Default Personal Value',
            roof: formData.roof || 'Default Roof',
            nextLevel: formData.nextLevel || 'Default Next Level',
            dropOfWater: formData.dropOfWater || 'Default Drop of Water',
            beliefs: formData.beliefs || 'Default Beliefs',
            selfVisions: formData.selfVisions || 'Default Self Visions',
            possibleObjections: formData.possibleObjections || 'Default Objections',
            ownCommunication: formData.ownCommunication || 'Default Communication',
            ...(extendedCampaignData.productId && { productId: extendedCampaignData.productId })
          };
          response = await PublicService.createPublic(publicData);
          if (response?.id) {
            updateCampaignData({
              ...publicData,
              publicId: response.id
            });
            markStepAsCompleted('public');
            shouldGoToNextStep = true;
          }
          break;
        }

        case 'expert': {
          const expertData = {
            name: formData.name || 'Default Name',
            areaOfExpertise: formData.areaOfExpertise || 'Default Area',
            biography: formData.bio || 'Default Biography',
            moment: formData.moment || 'Default Moment',
            dolor: formData.dolor || 'Default Dolor',
            credibility: formData.credibility || 'Default Credibility',
            recognition: formData.recognition || 'Default Recognition',
            trackRecord: formData.trackRecord || 'Default Track Record',
            howProductWorks: formData.howProductWorks || 'Default How Product Works',
            voicePersonality: formData.voicePersonality || 'Default Voice',
            essentialValues: formData.essentialValues || 'Default Values',
            enterprise: formData.enterprise || 'Default Enterprise',
            ...(extendedCampaignData.publicId && { publicId: extendedCampaignData.publicId })
          };
          response = await ExpertService.createExpert(expertData);
          if (response?.id) {
            updateCampaignData({
              expertName: expertData.name,
              expertise: expertData.areaOfExpertise,
              bio: expertData.biography,
              expertId: response.id
            });
            markStepAsCompleted('expert');
            shouldGoToNextStep = true;
          }
          break;
        }

        case 'campaign': {
          const campaignData = {
            name: formData.name || '',
            type: Number(formData.type) || 0,
            conscienceLevel: Number(formData.conscienceLevel) || 0,
            sophisticationLevel: Number(formData.sophisticationLevel) || 0,
            ideia: formData.ideia || '',
            emotion: formData.emotion || '',
            belief: formData.belief || '',
            productId: extendedCampaignData.productId,
            publicId: extendedCampaignData.publicId,
            expertId: extendedCampaignData.expertId,
          };
          response = await CampaignService.createCampaign(campaignData);
          if (response?.id) {
            updateCampaignData({
              campaignName: campaignData.name,
              campaignType: campaignData.type.toString(),
              conscienceLevel: campaignData.conscienceLevel,
              sophisticationLevel: campaignData.sophisticationLevel,
              ideia: campaignData.ideia,
              emotion: campaignData.emotion,
              belief: campaignData.belief,
              campaignId: response.id
            });
            markStepAsCompleted('campaign');
            shouldGoToNextStep = true;
          }
          break;
        }
      }

      // Navigate to the next step if the current step was successfully completed
      if (shouldGoToNextStep) {
        goToNextStep(activeTab);
      }
    } catch (error) {
      console.error('Error in form submission:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="h-[500px] bg-gray-50 p-6 overflow-y-auto">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-2xl font-bold mb-6">High copy</h1>

        <Tabs
          value={activeTab}
          onValueChange={handleTabChange}
          className="w-full"
        >
          <TabsList className="flex w-full overflow-x-auto py-2">
            <TabsTrigger
              value="product"
              className={`relative min-w-[100px] transition-colors ${isStepCompleted('product') ? 'bg-green-50 dark:bg-green-900/20' : ''} ${activeTab === 'product' ? 'bg-blue-50 dark:bg-blue-900/30' : ''}`}
            >
              1. Produto
              {isStepCompleted('product') && (
                <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-green-500"></span>
              )}
            </TabsTrigger>
            <TabsTrigger
              value="public"
              className={`relative transition-colors ${isStepCompleted('public') ? 'bg-green-50 dark:bg-green-900/20' : ''} ${activeTab === 'public' ? 'bg-blue-50 dark:bg-blue-900/30' : ''}`}
            >
              2. Público
              {isStepCompleted('public') && (
                <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-green-500"></span>
              )}
            </TabsTrigger>
            <TabsTrigger
              value="expert"
              className={`relative transition-colors ${isStepCompleted('expert') ? 'bg-green-50 dark:bg-green-900/20' : ''} ${activeTab === 'expert' ? 'bg-blue-50 dark:bg-blue-900/30' : ''}`}
            >
              3. Especialista
              {isStepCompleted('expert') && (
                <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-green-500"></span>
              )}
            </TabsTrigger>
            <TabsTrigger
              value="campaign"
              className={`relative transition-colors ${isStepCompleted('campaign') ? 'bg-green-50 dark:bg-green-900/20' : ''} ${activeTab === 'campaign' ? 'bg-blue-50 dark:bg-blue-900/30' : ''}`}
            >
              4. Campanha
              {isStepCompleted('campaign') && (
                <span className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-green-500"></span>
              )}
            </TabsTrigger>
          </TabsList>

          <div className="mt-6">
            <TabsContent value="product">
              <Card>
                <CardHeader>
                  <CardTitle>Informações do Produto</CardTitle>
                </CardHeader>
                <CardContent>
                  <ProductSetupStep
                    onSubmit={handleStepComplete}
                    onSuccess={() => handleStepComplete()}
                    initialData={{
                      name: extendedCampaignData.name,
                      benefits: extendedCampaignData.description,
                      socialProof: extendedCampaignData.socialProof,
                      metodology: extendedCampaignData.metodology,
                      guarantee: extendedCampaignData.guarantee,
                      deliverables: extendedCampaignData.deliverables ? Number(extendedCampaignData.deliverables) : undefined,
                      customerJourney: extendedCampaignData.customerJourney ? Number(extendedCampaignData.customerJourney) : undefined
                    }}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="public">
              <Card>
                <CardHeader>
                  <CardTitle>Público-Alvo</CardTitle>
                </CardHeader>
                <CardContent>
                  <PublicSetupStep
                    onNext={() => handleStepComplete()}
                    onSuccess={() => handleStepComplete()}
                    onSubmit={async (formData) => {
                      try {
                        const publicData = {
                          name: formData.publicName || 'Não informado',
                          local: 'Não informado',
                          familySituation: 'Não informado',
                          personality: 'Não informado',
                          hobbies: formData.interests || 'Não informado',
                          lifestyle: 'Não informado',
                          personalValue: 'Não informado',
                          roof: 'Não informado',
                          nextLevel: 'Não informado',
                          dropOfWater: 'Não informado',
                          beliefs: 'Não informado',
                          selfVisions: 'Não informado',
                          possibleObjections: formData.objections || formData.painPoints || 'Não informado',
                          ownCommunication: 'Não informado',
                          ageGroup: formData.ageGroup,
                          gender: formData.gender,
                          educationLevel: formData.educationLevel
                        };

                        const response = await PublicService.createPublic(publicData);
                        if (response?.id) {
                          updateCampaignData({
                            ...publicData,
                            publicId: response.id,
                            // Convert numeric values to strings to match CampaignData interface
                            ageGroup: publicData.ageGroup,
                            gender: publicData.gender?.toString(),
                            educationLevel: publicData.educationLevel?.toString()
                          });
                          markStepAsCompleted('public');
                          goToNextStep('public');
                        }
                      } catch (error) {
                        console.error('Error saving public data:', error);
                        throw error;
                      }
                    }}
                    initialData={{
                      publicName: extendedCampaignData.name || '',
                      ageGroup: extendedCampaignData.ageGroup ? Number(extendedCampaignData.ageGroup) : 0,
                      gender: extendedCampaignData.gender ? Number(extendedCampaignData.gender) : 0,
                      educationLevel: extendedCampaignData.educationLevel ? Number(extendedCampaignData.educationLevel) : 0,
                      interests: extendedCampaignData.hobbies || '',
                      painPoints: extendedCampaignData.possibleObjections || '',
                      objections: extendedCampaignData.possibleObjections || ''
                    }}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="expert">
              <Card>
                <CardHeader>
                  <CardTitle>Especialista</CardTitle>
                </CardHeader>
                <CardContent>
                  <ExpertSetupStep
                    onNext={() => handleStepComplete()}
                    onSuccess={() => handleStepComplete()}
                    initialData={{
                      name: extendedCampaignData.expertName || '',
                      areaOfExpertise: extendedCampaignData.expertise || '',
                      biography: extendedCampaignData.bio || '',
                      moment: extendedCampaignData.moment || '',
                      dolor: extendedCampaignData.dolor || '',
                      credibility: extendedCampaignData.credibility || '',
                      recognition: extendedCampaignData.recognition || '',
                      trackRecord: extendedCampaignData.trackRecord || '',
                      howProductWorks: extendedCampaignData.howProductWorks || '',
                      voicePersonality: extendedCampaignData.voicePersonality || '',
                      essentialValues: extendedCampaignData.essentialValues || '',
                      enterprise: extendedCampaignData.enterprise || ''
                    }}
                  />
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="campaign">
              <Card>
                <CardHeader>
                  <CardTitle>Campanha</CardTitle>
                </CardHeader>
                <CardContent>
                  <CampaignSetupStep
                    onNext={() => handleStepComplete()}
                    onSuccess={() => handleStepComplete()}
                    initialData={{
                      name: extendedCampaignData.campaignName || '',
                      type: extendedCampaignData.campaignType ? Number(extendedCampaignData.campaignType) : 0,
                      conscienceLevel: extendedCampaignData.conscienceLevel ? Number(extendedCampaignData.conscienceLevel) : 0,
                      sophisticationLevel: extendedCampaignData.sophisticationLevel ? Number(extendedCampaignData.sophisticationLevel) : 0,
                      ideia: extendedCampaignData.ideia || '',
                      emotion: extendedCampaignData.emotion || '',
                      belief: extendedCampaignData.belief || ''
                    }}
                  />
                </CardContent>
              </Card>
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </div>
  );
};

export default DashboardPage;
