import React from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from '@/components/atoms/card';
import { Button } from '@/components/atoms/button';
import { Loader2 } from 'lucide-react';
import { MarkdownRenderer } from '@/components/atoms/markdown-renderer';

type BaseContentPageProps = {
  title: string;
  description: string;
  children: React.ReactNode;
  onSubmit: () => Promise<void>;
  isLoading: boolean;
  result: string;
  error: string | null;
};

export const BaseContentPage: React.FC<BaseContentPageProps> = ({
  title,
  description,
  children,
  onSubmit,
  isLoading,
  result,
  error,
}) => {
  return (
    <div className="container mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">{title}</h1>
        <p className="text-gray-600">{description}</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Configurações</CardTitle>
          </CardHeader>
          <CardContent>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                onSubmit();
              }}
            >
              {children}

              <div className="mt-6">
                <Button type="submit" disabled={isLoading} className="w-full">
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Processando...
                    </>
                  ) : (
                    'Gerar Conteúdo'
                  )}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>

        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Resultado</CardTitle>
            </CardHeader>
            <CardContent>
              {error ? (
                <div className="text-red-500 p-4 bg-red-50 rounded-md">
                  {error}
                </div>
              ) : result ? (
                <div className="p-4 bg-gray-50 rounded-md">
                  <MarkdownRenderer content={result} />
                </div>
              ) : (
                <div className="text-gray-400 text-center p-8">
                  Seu conteúdo gerado aparecerá aqui
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
