import { useState, useEffect } from 'react';
import { ProductService, type Product } from '@/services/product.service';
import { toast } from '@/components/atoms/toaster';

export function useProducts() {
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchProducts = async () => {
    try {
      setIsLoading(true);
      const data = await ProductService.getAllProducts();
      setProducts(data);
      return data;
    } catch (err) {
      console.error('Error fetching products:', err);
      setError(err as Error);
      toast({
        title: 'Erro',
        description: 'Não foi possível carregar os produtos',
        variant: 'destructive',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchProducts().catch(() => {});
  }, []);

  return {
    products,
    isLoading,
    error,
    refresh: fetchProducts,
  };
}
