import React from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { But<PERSON> } from '../atoms/button';
import { Card, CardContent, CardHeader, CardTitle } from '../atoms/card';
import { Input } from '../atoms/input';
import { Label } from '../atoms/label';
import { Textarea } from '../atoms/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../atoms/select';
import {
  CampaignType,
  CampaignConscienceLevel,
  CampaignSophisticationLevel,
} from '../../enums';

type EnumType = Record<string, string | number>;

function getEnumEntries(enumObj: EnumType): [string, number][] {
  return Object.entries(enumObj)
    .filter(([key]) => isNaN(Number(key)))
    .map(([key, value]) => [key, value as number]);
}
import { CampaignService } from '@/services';

interface CampaignFormData {
  name: string;
  type: number;
  conscienceLevel: number;
  sophisticationLevel: number;
  ideia: string;
  emotion: string;
  belief: string;
}

interface CampaignSubmitData {
  type: number;
  conscienceLevel: number;
  sophisticationLevel: number;
  name: string;
  ideia: string;
  emotion: string;
  belief: string;
}

interface CampaignSetupStepProps {
  onSubmit?: (data: CampaignSubmitData) => void | Promise<void>;
  onNext?: () => void;
  onSuccess?: () => void;
  initialData?: Partial<CampaignFormData>;
}

const CampaignSetupStep: React.FC<CampaignSetupStepProps> = ({
  onSubmit,
  onNext,
  onSuccess,
  initialData = {},
}) => {
  const { control, handleSubmit, formState: { errors } } = useForm<CampaignFormData>({
    defaultValues: {
      name: '',
      type: 1,
      conscienceLevel: 1,
      sophisticationLevel: 1,
      ideia: '',
      emotion: '',
      belief: '',
      ...initialData,
    },
  });

  const handleFormSubmit = async (formData: CampaignFormData) => {
    try {
      // Ensure all required fields are present and have valid values
      const type = Number(formData.type);
      const conscienceLevel = Number(formData.conscienceLevel);
      const sophisticationLevel = Number(formData.sophisticationLevel);

      if (isNaN(type) || isNaN(conscienceLevel) || isNaN(sophisticationLevel)) {
        throw new Error('Invalid form data');
      }

      const submissionData: CampaignSubmitData = {
        type,
        conscienceLevel,
        sophisticationLevel,
        name: formData.name,
        ideia: formData.ideia,
        emotion: formData.emotion,
        belief: formData.belief,
      };

      if (onSubmit) {
        await onSubmit(submissionData);
      } else {
        await CampaignService.createCampaign(submissionData);
      }

      onNext?.();
      onSuccess?.();
    } catch (error) {
      console.error('Error submitting campaign data:', error);
      throw error;
    }
  };

  return (
    <form
      id="campaign-form"
      onSubmit={handleSubmit(handleFormSubmit as any)}
      className="space-y-6"
    >
      <div className="space-y-6">
        <Card>
          <CardHeader>
            <CardTitle>Configuração da Campanha</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Label htmlFor="name">Nome da Campanha</Label>
              <Controller
                name="name"
                control={control}
                rules={{ required: 'Campo obrigatório' }}
                render={({ field }) => (
                  <Input
                    id="name"
                    placeholder="Digite o nome da campanha"
                    {...field}
                  />
                )}
              />
              {errors.name && (
                <p className="text-sm text-red-500">{errors.name.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="type">Tipo de Campanha</Label>
              <Controller
                name="type"
                control={control}
                rules={{ required: 'Campo obrigatório' }}
                render={({ field }) => (
                  <Select
                    value={field.value?.toString()}
                    onValueChange={field.onChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o tipo de campanha" />
                    </SelectTrigger>
                    <SelectContent>
                      {getEnumEntries(CampaignType).map(([key, value]) => (
                        <SelectItem key={key} value={String(value)}>
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.type && (
                <p className="text-sm text-red-500">{errors.type.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="conscienceLevel">
                Nível de Consciência do Público
              </Label>
              <Controller
                name="conscienceLevel"
                control={control}
                rules={{ required: 'Campo obrigatório' }}
                render={({ field }) => (
                  <Select
                    value={field.value?.toString()}
                    onValueChange={field.onChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o nível de consciência" />
                    </SelectTrigger>
                    <SelectContent>
                      {getEnumEntries(CampaignConscienceLevel).map(([key, value]) => (
                        <SelectItem key={key} value={String(value)}>
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.conscienceLevel && (
                <p className="text-sm text-red-500">
                  {errors.conscienceLevel.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="sophisticationLevel">Nível de Sofisticação</Label>
              <Controller
                name="sophisticationLevel"
                control={control}
                rules={{ required: 'Campo obrigatório' }}
                render={({ field }) => (
                  <Select
                    value={field.value?.toString()}
                    onValueChange={field.onChange}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o nível de sofisticação" />
                    </SelectTrigger>
                    <SelectContent>
                      {getEnumEntries(CampaignSophisticationLevel).map(([key, value]) => (
                        <SelectItem key={key} value={String(value)}>
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.sophisticationLevel && (
                <p className="text-sm text-red-500">
                  {errors.sophisticationLevel.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="ideia">Ideia Principal</Label>
              <Controller
                name="ideia"
                control={control}
                rules={{ required: 'Campo obrigatório' }}
                render={({ field }) => (
                  <Textarea
                    id="ideia"
                    placeholder="Descreva a ideia principal da campanha..."
                    className="min-h-[120px]"
                    {...field}
                  />
                )}
              />
              {errors.ideia && (
                <p className="text-sm text-red-500">{errors.ideia.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="emotion">Emoção</Label>
              <Controller
                name="emotion"
                control={control}
                rules={{ required: 'Campo obrigatório' }}
                render={({ field }) => (
                  <Input
                    id="emotion"
                    placeholder="Qual emoção você quer despertar?"
                    {...field}
                  />
                )}
              />
              {errors.emotion && (
                <p className="text-sm text-red-500">{errors.emotion.message}</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="belief">Crença</Label>
              <Controller
                name="belief"
                control={control}
                rules={{ required: 'Campo obrigatório' }}
                render={({ field }) => (
                  <Input
                    id="belief"
                    placeholder="Qual crença você quer reforçar?"
                    {...field}
                  />
                )}
              />
              {errors.belief && (
                <p className="text-sm text-red-500">{errors.belief.message}</p>
              )}
            </div>

            <div className="flex justify-end">
              <Button
                type="submit"
                className="bg-blue-600 hover:bg-blue-700 text-white"
              >
                Próximo
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </form>
  );
};

export default CampaignSetupStep;
