import { useEffect, useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { MessageService, type Copy } from '@/services/message.service';
import { Button } from '@/components/atoms/button';
import { Skeleton } from '@/components/atoms/skeleton';
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from '@/components/atoms/card';
import { Badge } from '@/components/atoms/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/atoms/tabs';
import { ArrowLeft, Calendar, MessageSquare, User, Code, FileText, Copy as CopyIcon } from 'lucide-react';
import { useToast } from '@/components/atoms/use-toast';
import { MarkdownRenderer } from '@/components/atoms/markdown-renderer';

export function CopyPage() {
  const { id } = useParams<{ id: string }>();
  const [copy, setCopy] = useState<Copy | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('response');
  const navigate = useNavigate();
  const { toast } = useToast();

  useEffect(() => {
    const fetchCopy = async () => {
      if (!id) return;
      
      try {
        const data = await MessageService.getById(id);
        setCopy({
          ...data,
          title: data.title || `Copy ${data.id}`,
          content: data.content || data.aiResponse || data.userPrompt || '',
          updatedAt: data.updatedAt || data.createdAt
        });
      } catch (error) {
        console.error('Error fetching copy:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCopy();
  }, [id]);

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '';
    try {
      return new Date(dateString).toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: 'long',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return '';
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast({
      title: 'Copiado!',
      description: 'O texto foi copiado para a área de transferência.',
    });
  };

  if (loading || !copy) {
    return (
      <div className="container mx-auto p-4 max-w-4xl">
        <div className="mb-6">
          <Skeleton className="h-8 w-32" />
        </div>
        <div className="space-y-4">
          <Skeleton className="h-6 w-1/4" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-3/4" />
        </div>
      </div>
    );
  }


  return (
    <div className="container mx-auto p-4 max-w-5xl">
      <div className="mb-6 flex justify-between items-center">
        <Button 
          variant="ghost" 
          onClick={() => navigate('/app/copies')}
          className="px-0 hover:bg-transparent"
        >
          <ArrowLeft className="h-4 w-4 mr-2" /> Voltar para a lista
        </Button>
        
        <div className="flex items-center space-x-2">
          {copy.provider && (
            <Badge variant="outline" className="flex items-center">
              <Code className="h-3 w-3 mr-1" />
              {copy.provider}
            </Badge>
          )}
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => copyToClipboard(copy.content || '')}
            className="flex items-center"
          >
            <CopyIcon className="h-3.5 w-3.5 mr-1.5" />
            Copiar
          </Button>
        </div>
      </div>
      
      <Card className="overflow-hidden">
        <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-slate-800 dark:to-slate-900 pb-4">
          <div className="flex justify-between items-start">
            <div>
              <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
                {copy.title}
              </CardTitle>
              <CardDescription className="mt-1 flex items-center text-sm text-gray-500 dark:text-gray-400">
                <Calendar className="h-4 w-4 mr-1.5" />
                Criado em {formatDate(copy.createdAt)}
              </CardDescription>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="p-0">
          <Tabs 
            defaultValue="response" 
            value={activeTab} 
            onValueChange={setActiveTab}
            className="w-full"
          >
            <TabsList className="w-full justify-start rounded-none border-b bg-transparent p-0">
              <TabsTrigger 
                value="response" 
                className="relative h-10 rounded-none border-b-2 border-transparent bg-transparent px-4 pb-4 pt-2 font-semibold text-muted-foreground shadow-none transition-none data-[state=active]:border-primary data-[state=active]:text-foreground data-[state=active]:shadow-none"
              >
                <MessageSquare className="mr-2 h-4 w-4" />
                Resposta
              </TabsTrigger>
              {copy.userPrompt && (
                <TabsTrigger 
                  value="prompt" 
                  className="relative h-10 rounded-none border-b-2 border-transparent bg-transparent px-4 pb-4 pt-2 font-semibold text-muted-foreground shadow-none transition-none data-[state=active]:border-primary data-[state=active]:text-foreground data-[state=active]:shadow-none"
                >
                  <User className="mr-2 h-4 w-4" />
                  Sua solicitação
                </TabsTrigger>
              )}
              <TabsTrigger 
                value="details" 
                className="relative h-10 rounded-none border-b-2 border-transparent bg-transparent px-4 pb-4 pt-2 font-semibold text-muted-foreground shadow-none transition-none data-[state=active]:border-primary data-[state=active]:text-foreground data-[state=active]:shadow-none"
              >
                <FileText className="mr-2 h-4 w-4" />
                Detalhes
              </TabsTrigger>
            </TabsList>
            
            <div className="p-6">
              <TabsContent value="response" className="mt-0">
                <div className="rounded-lg border p-4 bg-white dark:bg-slate-900">
                  <MarkdownRenderer content={copy.content || ''} />
                </div>
              </TabsContent>
              
              {copy.userPrompt && (
                <TabsContent value="prompt" className="mt-0">
                  <div className="bg-slate-50 dark:bg-slate-900/50 p-4 rounded-lg">
                    <MarkdownRenderer 
                      content={copy.userPrompt || ''} 
                      className="text-sm" 
                    />
                  </div>
                </TabsContent>
              )}
              
              <TabsContent value="details" className="mt-0">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-muted-foreground">ID</h4>
                    <div className="flex items-center">
                      <code className="relative rounded bg-muted px-[0.3rem] py-[0.2rem] font-mono text-sm font-semibold">
                        {copy.id}
                      </code>
                      <Button 
                        variant="ghost" 
                        size="icon" 
                        className="h-6 w-6 ml-2"
                        onClick={() => copyToClipboard(copy.id)}
                      >
                        <CopyIcon className="h-3.5 w-3.5" />
                        <span className="sr-only">Copiar ID</span>
                      </Button>
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <h4 className="text-sm font-medium text-muted-foreground">Criado em</h4>
                    <p className="text-sm">{formatDate(copy.createdAt)}</p>
                  </div>
                  
                  {copy.updatedAt && copy.updatedAt !== copy.createdAt && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-muted-foreground">Atualizado em</h4>
                      <p className="text-sm">{formatDate(copy.updatedAt)}</p>
                    </div>
                  )}
                  
                  {copy.provider && (
                    <div className="space-y-2">
                      <h4 className="text-sm font-medium text-muted-foreground">Provedor</h4>
                      <div className="flex items-center">
                        <Code className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span className="text-sm capitalize">{copy.provider}</span>
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </CardContent>
        
        <CardFooter className="flex justify-between border-t bg-muted/20 p-4">
          <div className="text-xs text-muted-foreground">
            ID: {copy.id}
          </div>
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              size="sm"
              onClick={() => copyToClipboard(copy.content || '')}
            >
              <CopyIcon className="mr-2 h-3.5 w-3.5" />
              Copiar texto
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
}

export default CopyPage;
