// Remove unused React import
import { useF<PERSON>, Controller } from 'react-hook-form';
import { Button } from '../atoms/button';
import { Card, CardContent, CardHeader, CardTitle } from '../atoms/card';
import { Input } from '../atoms/input';
import { Label } from '../atoms/label';
import { Textarea } from '../atoms/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../atoms/select';
import {
  ProductDeliverables,
  ProductCustomerJourney,
} from '../../enums';
import { ProductService } from '@/services';
import type { ProductSetupRequest } from '@/types/product.types';

type ProductSetupStepProps = {
  onSubmit?: (data: ProductSetupRequest) => void;
  onNext?: () => void;
  onSuccess?: () => void;
  initialData?: Partial<ProductSetupRequest>;
};

const ProductSetupStep = ({
  onSubmit,
  onNext,
  onSuccess,
  initialData = {},
}: ProductSetupStepProps) => {
  const { control, handleSubmit } = useForm<ProductSetupRequest>({
    defaultValues: {
      name: '',
      benefits: '',
      socialProof: '',
      metodology: '',
      guarantee: '',
      deliverables: ProductDeliverables.SessionMentoring,
      customerJourney: ProductCustomerJourney.OneTwoMonths,
      ...initialData,
    } as ProductSetupRequest,
    mode: 'onChange',
  });

  const handleFormSubmit = async (data: ProductSetupRequest) => {
    try {
      if (onSubmit) {
        await onSubmit(data);
      } else if (data.name) {
        try {
          const payload = {
            ...data,
            deliverables: Number(data.deliverables),
            customerJourney: Number(data.customerJourney),
          };
          await ProductService.createProduct(payload);
        } catch (error) {
          console.error('Error creating product:', error);
        }
      }

      if (onNext) onNext();
      if (onSuccess) onSuccess();
    } catch (error) {
      console.error('Error in form submission:', error);
      if (onNext) onNext();
      if (onSuccess) onSuccess();
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Configuração do Produto</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="name">Nome do Produto</Label>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <Input
                  id="name"
                  placeholder="Nome do produto ou serviço (opcional)"
                  {...field}
                />
              )}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="benefits">Benefícios do Produto</Label>
            <Controller
              name="benefits"
              control={control}
              render={({ field }) => (
                <Textarea
                  id="benefits"
                  placeholder="Liste os principais benefícios do produto (opcional)"
                  className="min-h-[100px]"
                  {...field}
                />
              )}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="socialProof">Prova Social</Label>
            <Controller
              name="socialProof"
              control={control}
              render={({ field }) => (
                <Textarea
                  id="socialProof"
                  placeholder="Depoimentos ou provas sociais do produto (opcional)"
                  className="min-h-[100px]"
                  {...field}
                />
              )}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="metodology">Metodologia</Label>
            <Controller
              name="metodology"
              control={control}
              render={({ field }) => (
                <Textarea
                  id="metodology"
                  placeholder="Descreva a metodologia do produto (opcional)"
                  className="min-h-[100px]"
                  {...field}
                />
              )}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="guarantee">Garantia</Label>
            <Controller
              name="guarantee"
              control={control}
              render={({ field }) => (
                <Textarea
                  id="guarantee"
                  placeholder="Informe as garantias do produto (opcional)"
                  className="min-h-[100px]"
                  {...field}
                />
              )}
            />
          </div>

          <div className="space-y-2">
            <Label>Entregáveis</Label>
            <Controller
              name="deliverables"
              control={control}
              render={({ field }) => (
                <Select
                  value={field.value?.toString() || ''}
                  onValueChange={(value) => field.onChange(Number(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione os entregáveis" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(ProductDeliverables)
                      .filter(([key]) => isNaN(Number(key)))
                      .map(([key, value]) => (
                        <SelectItem key={key} value={value.toString()}>
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          <div className="space-y-2">
            <Label>Jornada do Cliente</Label>
            <Controller
              name="customerJourney"
              control={control}
              render={({ field }) => (
                <Select
                  value={field.value?.toString() || ''}
                  onValueChange={(value) => field.onChange(Number(value))}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione a jornada do cliente" />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(ProductCustomerJourney)
                      .filter(([key]) => isNaN(Number(key)))
                      .map(([key, value]) => (
                        <SelectItem key={key} value={value.toString()}>
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              )}
            />
          </div>

          <div className="flex justify-between">
            <Button 
              type="button" 
              variant="outline" 
              onClick={(e) => {
                e.preventDefault();
                if (onSubmit) {
                  handleSubmit(onSubmit)();
                }
              }}
            >
              Voltar
            </Button>
            <Button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Próximo
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default ProductSetupStep;
