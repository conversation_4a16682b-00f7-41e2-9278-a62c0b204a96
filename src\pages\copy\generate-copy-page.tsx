import { Button } from '@/components/atoms/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/atoms/card';
import { Label } from '@/components/atoms/label';
import { MarkdownRenderer } from '@/components/atoms/markdown-renderer';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/atoms/select';
import { Textarea } from '@/components/atoms/textarea';
import { API_BASE_URL } from '@/config';
import { CopyService } from '@/services/copy.service';
import { Loader2 } from 'lucide-react';
import { useEffect, useState } from 'react';

interface BaseItem {
  id: string;
  name: string;
}

export default function GenerateCopyPage() {
  const [selectedExpert, setSelectedExpert] = useState('');
  const [selectedProduct, setSelectedProduct] = useState('');
  const [selectedCampaign, setSelectedCampaign] = useState('');
  const [selectedPublic, setSelectedPublic] = useState('');
  const [copyRequest, setCopyRequest] = useState('');
  const [generatedCopy, setGeneratedCopy] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  const [experts, setExperts] = useState<BaseItem[]>([]);
  const [products, setProducts] = useState<BaseItem[]>([]);
  const [campaigns, setCampaigns] = useState<BaseItem[]>([]);
  const [publics, setPublics] = useState<BaseItem[]>([]);
  const [isLoadingData, setIsLoadingData] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Fetch experts
        const expertsRes = await fetch(`${API_BASE_URL}/expert-setup`);
        const expertsData = await expertsRes.json();
        setExperts(
          Array.isArray(expertsData) ? expertsData : expertsData?.data || []
        );

        // Fetch products
        const productsRes = await fetch(
          `${API_BASE_URL}/product-setup`
        );
        const productsData = await productsRes.json();
        setProducts(
          Array.isArray(productsData) ? productsData : productsData?.data || []
        );

        // Fetch campaigns
        const campaignsRes = await fetch(
          `${API_BASE_URL}/campaign-setup?page=1&limit=100`
        );
        const campaignsData = await campaignsRes.json();
        setCampaigns(
          Array.isArray(campaignsData)
            ? campaignsData
            : campaignsData?.data?.items || campaignsData?.data || []
        );

        // Fetch publics
        const publicsRes = await fetch(`${API_BASE_URL}/public-setup`);
        const publicsData = await publicsRes.json();
        setPublics(
          Array.isArray(publicsData) ? publicsData : publicsData?.data || []
        );
      } catch (error) {
        console.error('Erro ao buscar dados:', error);
      } finally {
        setIsLoadingData(false);
      }
    };

    fetchData();
  }, []);

  const handleGenerateCopy = async () => {
    if (
      !selectedCampaign ||
      !selectedExpert ||
      !selectedProduct ||
      !selectedPublic
    ) {
      alert('Por favor, preencha todos os campos obrigatórios');
      return;
    }

    setIsLoading(true);

    try {
      const response = await CopyService.generateCompleteCopy({
        campaignId: String(selectedCampaign),
        expertId: String(selectedExpert),
        productId: String(selectedProduct),
        publicId: String(selectedPublic),
        copyRequest: copyRequest || '',
        provider: 'chatgpt',
      });
      setGeneratedCopy(response.generatedCopy);
    } catch (error) {
      console.error('Erro ao gerar copy:', error);
      alert('Ocorreu um erro ao gerar o copy. Por favor, tente novamente.');
    } finally {
      setIsLoading(false);
    }
  };

  if (isLoadingData) {
    return (
      <div className="container mx-auto p-4">
        <div className="flex justify-center items-center h-64">
          <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
          <span className="ml-2">Carregando dados...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">Gerar Copy Completa</h1>
        <p className="text-gray-600">
          Preencha os campos abaixo para gerar um copy personalizado para sua
          campanha.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Configurações da Copy</CardTitle>
              <CardDescription>
                Selecione os elementos para compor sua copy
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="expert">Especialista *</Label>
                <Select
                  onValueChange={(value) => setSelectedExpert(String(value))}
                  value={selectedExpert}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um especialista" />
                  </SelectTrigger>
                  <SelectContent>
                    {experts.map((expert) => (
                      <SelectItem key={expert.id} value={expert.id}>
                        {expert.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="product">Produto *</Label>
                <Select
                  onValueChange={(value) => setSelectedProduct(String(value))}
                  value={selectedProduct}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um produto" />
                  </SelectTrigger>
                  <SelectContent>
                    {products.map((product) => (
                      <SelectItem key={product.id} value={product.id}>
                        {product.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="campaign">Campanha *</Label>
                <Select
                  onValueChange={(value) => setSelectedCampaign(String(value))}
                  value={selectedCampaign}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione uma campanha" />
                  </SelectTrigger>
                  <SelectContent>
                    {campaigns.map((campaign) => (
                      <SelectItem key={campaign.id} value={campaign.id}>
                        {campaign.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="public">Público *</Label>
                <Select
                  onValueChange={(value) => setSelectedPublic(String(value))}
                  value={selectedPublic}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um público" />
                  </SelectTrigger>
                  <SelectContent>
                    {publics.map((pub) => (
                      <SelectItem key={pub.id} value={pub.id}>
                        {pub.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="copyRequest">Instruções para o copy *</Label>
                <Textarea
                  id="copyRequest"
                  placeholder="Descreva como você gostaria que fosse o copy..."
                  value={copyRequest}
                  onChange={(e) => setCopyRequest(e.target.value)}
                  rows={5}
                />
                <p className="text-sm text-gray-500">
                  Descreva com detalhes o estilo e o tom que deseja para o copy.
                </p>
              </div>

              <Button
                onClick={handleGenerateCopy}
                disabled={isLoading}
                className="w-full"
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Gerando...
                  </>
                ) : (
                  'Gerar Copy'
                )}
              </Button>
            </CardContent>
          </Card>
        </div>

        <div className="space-y-6">
          <Card className="h-full">
            <CardHeader>
              <CardTitle>Copy Gerado</CardTitle>
              <CardDescription>
                {generatedCopy
                  ? 'Seu copy foi gerado com sucesso!'
                  : 'O copy gerado aparecerá aqui.'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {generatedCopy ? (
                  <div className="p-6 bg-white border rounded-md shadow-sm min-h-[400px] overflow-auto">
                    <MarkdownRenderer content={generatedCopy} />
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-[400px] bg-gray-50 rounded-md text-gray-400 border-2 border-dashed">
                    <p>Nenhum copy gerado ainda</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
