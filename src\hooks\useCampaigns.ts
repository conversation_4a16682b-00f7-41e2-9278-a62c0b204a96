import { useState, useEffect } from 'react';
import { CampaignService } from '@/services/campaign.service';
import type { PaginatedResponse } from '@/types/api.types';
import type { CampaignResponse } from '@/types/campaign.types';
import { toast } from '@/components/atoms/toaster';

interface UseCampaignsReturn {
  campaigns: CampaignResponse[];
  isLoading: boolean;
  error: Error | null;
  pagination: Omit<PaginatedResponse<CampaignResponse>, 'items'>;
  refresh: () => Promise<CampaignResponse[]>;
  loadPage: (page: number) => Promise<void>;
}

export function useCampaigns(): UseCampaignsReturn {
  const [campaigns, setCampaigns] = useState<CampaignResponse[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [pagination, setPagination] = useState<
    Omit<PaginatedResponse<CampaignResponse>, 'items'>
  >({
    total: 0,
    page: 1,
    limit: 10,
    totalPages: 1,
    hasNext: false,
    hasPrevious: false,
  });

  const fetchCampaigns = async (
    page: number = 1,
    limit: number = 10
  ): Promise<CampaignResponse[]> => {
    try {
      setIsLoading(true);
      const response = await CampaignService.getAllCampaigns(page, limit);

      // Handle paginated response
      if (response && 'items' in response) {
        setCampaigns(response.items);
        const { items, ...paginationData } = response;
        setPagination(paginationData);
        return items;
      }

      // Fallback for non-paginated response
      const campaignsList = Array.isArray(response) ? response : [];
      setCampaigns(campaignsList);
      return campaignsList;
    } catch (err) {
      console.error('Error fetching campaigns:', err);
      setError(err as Error);
      toast({
        title: 'Erro',
        description: 'Não foi possível carregar as campanhas',
        variant: 'destructive',
      });
      throw err;
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchCampaigns().catch(() => {});
  }, []);

  const loadPage = async (page: number) => {
    await fetchCampaigns(page, pagination.limit);
  };

  return {
    campaigns,
    isLoading,
    error,
    pagination,
    refresh: () => fetchCampaigns(pagination.page, pagination.limit),
    loadPage,
  };
}
