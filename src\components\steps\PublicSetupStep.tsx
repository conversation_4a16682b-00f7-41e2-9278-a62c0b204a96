import React from 'react';
import { useF<PERSON>, Controller } from 'react-hook-form';
import type { SubmitHandler } from 'react-hook-form';
import { Button } from '../atoms/button';
import { Card, CardContent, CardHeader, CardTitle } from '../atoms/card';
import { Input } from '../atoms/input';
import { Label } from '../atoms/label';
import { Textarea } from '../atoms/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../atoms/select';
import {
  PublicAgeGroup,
  PublicEducationLevel,
  PublicGender,
} from '../../enums';
import { PublicService } from '@/services';

interface PublicFormData {
  publicName: string;
  ageGroup: number;
  gender: number;
  educationLevel: number;
  interests: string;
  painPoints: string;
  objections: string;
  [key: string]: any; // Permite acesso por string index
}

interface PublicSetupStepProps {
  onSubmit?: (data: PublicFormData) => Promise<void> | void;
  onNext?: () => void;
  onSuccess?: () => void;
  initialData?: Partial<PublicFormData>;
}

const PublicSetupStep: React.FC<PublicSetupStepProps> = ({
  onSubmit,
  onNext,
  onSuccess,
  initialData = {},
}) => {
  const {
    register,
    handleSubmit,
    formState: { errors },
    control,
  } = useForm<PublicFormData>({
    defaultValues: {
      publicName: '',
      ageGroup: 0,
      gender: 0,
      educationLevel: 0,
      interests: '',
      painPoints: '',
      objections: '',
      ...initialData,
    },
  });

  // Helper function to get enum entries
  const getEnumEntries = (enumObj: Record<string, any>): Array<[string, number]> => {
    return Object.entries(enumObj)
      .filter(([key]) => isNaN(Number(key)))
      .map(([key, value]) => [key, value as number]);
  };

  const handleFormSubmit: SubmitHandler<PublicFormData> = async (formData: PublicFormData) => {
    try {
      // Mapeando os dados do formulário para o formato esperado pela API
      const requestData = {
        name: formData.publicName || 'Não informado',
        local: 'Não informado',
        familySituation: 'Não informado',
        personality: 'Não informado',
        hobbies: formData.interests || 'Não informado',
        lifestyle: 'Não informado',
        personalValue: 'Não informado',
        roof: 'Não informado',
        nextLevel: 'Não informado',
        dropOfWater: 'Não informado',
        beliefs: 'Não informado',
        selfVisions: 'Não informado',
        possibleObjections: formData.objections || formData.painPoints || 'Não informado',
        ownCommunication: 'Não informado',
        // Campos adicionais que podem ser úteis
        ageGroup: formData.ageGroup,
        gender: formData.gender,
        educationLevel: formData.educationLevel
      };

      if (onSubmit) {
        // Se houver um onSubmit personalizado, use-o
        await onSubmit(formData);
      } else {
        // Caso contrário, use o serviço diretamente
        await PublicService.createPublic(requestData);
      }

      onNext?.();
      onSuccess?.();
    } catch (error) {
      console.error('Error submitting public data:', error);
      throw error;
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Configuração do Público</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="publicName">Nome do Público</Label>
            <Input
              id="publicName"
              {...register('publicName', { required: 'Campo obrigatório' })}
              placeholder="Ex: Empreendedores iniciantes"
            />
            {errors.publicName && (
              <p className="text-sm text-red-500">
                {errors.publicName.message as string}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="painPoints">Dores e Desafios</Label>
            <Textarea
              id="painPoints"
              {...register('painPoints', { required: 'Campo obrigatório' })}
              placeholder="Quais são as principais dores e desafios do público?"
              className="min-h-[100px]"
            />
            {errors.painPoints && (
              <p className="text-sm text-red-500">
                {errors.painPoints.message as string}
              </p>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="ageGroup">Faixa Etária</Label>
              <Controller
                name="ageGroup"
                control={control}
                rules={{ required: 'Campo obrigatório' }}
                render={({ field }: { field: any }) => (
                  <Select
                    onValueChange={(value) => field.onChange(Number(value))}
                    value={field.value?.toString() ?? ''}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione a faixa etária" />
                    </SelectTrigger>
                    <SelectContent>
                      {getEnumEntries(PublicAgeGroup).map(([key, value]) => (
                        <SelectItem key={key} value={value.toString()}>
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.ageGroup && (
                <p className="text-sm text-red-500">Campo obrigatório</p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="gender">Gênero</Label>
              <Controller
                name="gender"
                control={control}
                rules={{ required: 'Campo obrigatório' }}
                render={({ field }: { field: any }) => (
                  <Select
                    onValueChange={(value) => field.onChange(Number(value))}
                    value={field.value?.toString() ?? ''}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Selecione o gênero" />
                    </SelectTrigger>
                    <SelectContent>
                      {getEnumEntries(PublicGender).map(([key, value]) => (
                        <SelectItem key={key} value={value.toString()}>
                          {key}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                )}
              />
              {errors.gender && (
                <p className="text-sm text-red-500">Campo obrigatório</p>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="educationLevel">Nível de Escolaridade</Label>
            <Controller
              name="educationLevel"
              control={control}
              rules={{ required: 'Campo obrigatório' }}
              render={({ field }: { field: any }) => (
                <Select
                  onValueChange={(value) => field.onChange(Number(value))}
                  value={field.value?.toString() ?? ''}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione o nível de escolaridade" />
                  </SelectTrigger>
                  <SelectContent>
                    {getEnumEntries(PublicEducationLevel).map(([key, value]) => (
                      <SelectItem key={key} value={value.toString()}>
                        {key.replace(/([A-Z])/g, ' $1').trim()}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            />
            {errors.educationLevel && (
              <p className="text-sm text-red-500">Campo obrigatório</p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="interests">Interesses</Label>
            <Textarea
              id="interests"
              {...register('interests', { required: 'Campo obrigatório' })}
              placeholder="Quais são os principais interesses do público?"
              className="min-h-[100px]"
            />
            {errors.interests && (
              <p className="text-sm text-red-500">
                {errors.interests.message as string}
              </p>
            )}
          </div>

          <div className="space-y-2">
            <Label htmlFor="objections">Possíveis Objeções</Label>
            <textarea
              id="objections"
              {...register('objections', { required: 'Campo obrigatório' })}
              className="w-full p-2 border rounded-md min-h-[100px]"
              placeholder="Quais objeções seu público pode ter em relação à sua oferta?"
            />
            {errors.objections && (
              <p className="text-sm text-red-500">
                {errors.objections.message as string}
              </p>
            )}
          </div>

          <div className="flex justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => window.history.back()}
            >
              Voltar
            </Button>
            <Button
              type="submit"
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Próximo
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default PublicSetupStep;
