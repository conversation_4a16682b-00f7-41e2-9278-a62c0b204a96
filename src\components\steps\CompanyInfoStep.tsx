import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { Button } from '../atoms/button';
import { Card, CardContent, CardHeader, CardTitle } from '../atoms/card';
import { Editor } from 'primereact/editor';
import 'primereact/resources/themes/lara-light-indigo/theme.css';
import 'primereact/resources/primereact.min.css';
import 'primeicons/primeicons.css';

interface CompanyInfoStepProps {
  onSubmit: (data: any) => void;
  onNext: () => void;
  initialData?: any;
}

const CompanyInfoStep: React.FC<CompanyInfoStepProps> = ({
  onSubmit,
  onNext,
  initialData = {},
}) => {
  const { handleSubmit, setValue } = useForm({
    defaultValues: initialData,
  });

  const [companyInfo, setCompanyInfo] = useState(initialData.companyInfo || '');

  useEffect(() => {
    setValue('companyInfo', companyInfo);
  }, [companyInfo, setValue]);

  const handleFormSubmit = (data: any) => {
    onSubmit(data);
    onNext();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Informações da Empresa</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
          <div className="space-y-2">
            <p className="text-sm text-gray-600 mb-4">
              Por favor, nos conte sobre sua empresa. Quanto mais detalhes você
              fornecer, melhor poderemos criar um copy personalizado e eficaz
              para sua campanha. Inclua informações como histórico, valores,
              missão, visão, diferenciais competitivos e qualquer outra
              informação relevante que ajude a transmitir a essência do seu
              negócio.
            </p>
            <div className="h-96">
              <Editor
                value={companyInfo}
                onTextChange={(e) => setCompanyInfo(e.htmlValue || '')}
                style={{ height: '100%' }}
                placeholder="Descreva sua empresa aqui..."
              />
            </div>
          </div>
          <div className="flex justify-end">
            <Button type="submit">Próximo</Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default CompanyInfoStep;
