import { API_BASE_URL } from '@/config';
import { useEffect, useState } from 'react';

interface Public {
  id: string;
  name: string;
  description?: string;
  size?: number;
  location?: string;
  interests?: string[];
  createdAt?: string;
  updatedAt?: string;
}

export default function PublicPage() {
  const [publics, setPublics] = useState<Public[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchPublicData = async () => {
      try {
        const response = await fetch(`${API_BASE_URL}/public-setup`);
        const data = await response.json();
      
        setPublics(Array.isArray(data) ? data : data?.data || []);
      } catch (error) {
        console.error('Erro ao buscar públicos:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPublicData();
  }, []);

  const formatDate = (dateString?: string) => {
    if (!dateString) return 'Não definida';
    return new Date(dateString).toLocaleDateString('pt-BR');
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 p-8">
        <div className="max-w-7xl mx-auto">
          <h1 className="text-3xl font-bold text-gray-900 mb-8">
            Carregando públicos...
          </h1>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3].map((i) => (
              <div
                key={i}
                className="bg-white p-6 rounded-lg shadow-sm border border-gray-100 animate-pulse"
              >
                <div className="h-6 bg-gray-200 rounded w-3/4 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 p-4 md:p-8">
      <div className="max-w-7xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          Públicos
          <span className="ml-3 text-sm font-normal text-blue-600 bg-blue-50 px-3 py-1 rounded-full">
            {publics.length} públicos
          </span>
        </h1>

        {publics.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">Nenhum público encontrado</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {publics.map((pub) => (
              <div
                key={pub.id}
                className="bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow border border-gray-100 overflow-hidden"
              >
                <div className="p-6">
                  <div className="flex justify-between items-start">
                    <h2 className="text-xl font-semibold text-gray-900 mb-2">
                      {pub.name || 'Público sem nome'}
                    </h2>
                    {pub.size !== undefined && (
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-purple-50 text-purple-700">
                        {pub.size.toLocaleString('pt-BR')} pessoas
                      </span>
                    )}
                  </div>

                  {pub.description && (
                    <p className="mt-3 text-gray-600 text-sm">
                      {pub.description}
                    </p>
                  )}

                  <div className="mt-4 space-y-2">
                    {pub.location && (
                      <div className="flex items-center text-gray-600">
                        <svg
                          className="h-5 w-5 mr-2 text-gray-400"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                          />
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                          />
                        </svg>
                        <span className="text-sm">{pub.location}</span>
                      </div>
                    )}

                    {pub.interests && pub.interests.length > 0 && (
                      <div className="mt-3">
                        <p className="text-sm font-medium text-gray-500 mb-1">
                          Interesses:
                        </p>
                        <div className="flex flex-wrap gap-1">
                          {pub.interests.map((interest, index) => (
                            <span
                              key={index}
                              className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                            >
                              {interest}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>

                  <div className="mt-4 pt-4 border-t border-gray-100">
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>Criado em: {formatDate(pub.createdAt)}</span>
                      <span>ID: {String(pub.id).substring(0, 6)}...</span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
