import { apiClient } from './api';
import type { ApiResponse } from '@/types';

export interface GenerateContentRequest {
  prompt: string;
  preferredProvider?: 'chatgpt' | 'gemini';
}

export interface GenerateCompleteCopyRequest {
  campaignId: string;
  expertId: string;
  productId: string;
  publicId: string;
  copyRequest: string;
  provider?: 'chatgpt' | 'gemini';
}

export interface AnalyzeWebsiteRequest {
  url: string;
  provider?: 'chatgpt' | 'gemini';
}

export interface ContentResponse extends ApiResponse {
  content: string;
  provider: string;
  analysis?: string;
  message?: string;
}

export interface CompleteCopyResponse extends ApiResponse {
  generatedCopy: string;
  copyRequest: string;
  provider: string;
}

export interface WebsiteAnalysisResponse extends ApiResponse {
  url: string;
  analysis: string;
  provider: string;
}

export class ContentService {
  private static readonly BASE_ENDPOINT = '/content';

  static async generateContent(
    data: GenerateContentRequest
  ): Promise<ContentResponse> {
    const response = await apiClient.post<ContentResponse>(
      `${this.BASE_ENDPOINT}/generate`,
      data
    );
    return response.data;
  }

  static async analyzeFile(
    formData: FormData,
    provider: string = 'chatgpt'
  ): Promise<ContentResponse> {
    if (!formData.has('provider')) {
      formData.append('provider', provider);
    }

    const response = await apiClient.post<ContentResponse>(
      `${ContentService.BASE_ENDPOINT}/analyze-file`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    return response.data;
  }

  static async analyzeWebsite(
    data: AnalyzeWebsiteRequest
  ): Promise<ContentResponse> {
    const response = await apiClient.post<ContentResponse>(
      `${ContentService.BASE_ENDPOINT}/analyze-website`,
      {
        url: data.url,
        provider: data.provider || 'chatgpt',
      }
    );

    return response.data;
  }
}

export default ContentService;
