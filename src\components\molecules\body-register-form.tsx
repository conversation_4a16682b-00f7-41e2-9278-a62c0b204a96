import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { FaApple, FaGoogle } from 'react-icons/fa';

import * as z from 'zod';
import { Button } from '../atoms';
import { ButtonWithIcon } from './button-with-icon';
import { InputLabel } from './input-label';
import { useNavigate } from 'react-router-dom';

interface BodyRegisterFormProps {
  onSubmit: (data: registerSchemaProps) => void;
}

const registerSchema = z
  .object({
    name: z.string().nonempty('O nome é obrigatório'),
    email: z.string().nonempty('O email é obrigatório').email('Email inválido'),
    confirmEmail: z.string().nonempty('Confirmação de email é obrigatória'),
    password: z
      .string()
      .nonempty('A senha é obrigatória')
      .min(6, 'A senha deve ter pelo menos 6 caracteres'),
  })
  .refine((data) => data.email === data.confirmEmail, {
    path: ['confirmEmail'],
    message: 'Os emails não coincidem',
  });

export type registerSchemaProps = z.infer<typeof registerSchema>;

export const BodyRegisterForm = ({ onSubmit }: BodyRegisterFormProps) => {
  const navigate = useNavigate();

  const {
    handleSubmit,
    formState: { errors },
    register,
  } = useForm<registerSchemaProps>({
    resolver: zodResolver(registerSchema),
  });

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className="flex flex-col items-center w-full grow-1 p-3 sm:p-6 mt-6 sm:mt-14"
    >
      <h1 className="text-2xl sm:text-3xl font-semibold text-center">
        Create Account
      </h1>
      <h6 className="text-xs sm:text-sm text-gray-500 mt-4 text-center px-4">
        Enter your details to create your account
      </h6>

      <div className="w-full h-full mt-6 sm:mt-10 flex flex-col px-4 sm:px-8 lg:px-12">
        <InputLabel
          type="text"
          label="Name"
          placeholder="Full name"
          {...register('name')}
        />
        {errors.name && (
          <p className="text-red-500 text-[10px] mt-1">{errors.name.message}</p>
        )}

        <InputLabel
          type="email"
          label="Email"
          placeholder="Email"
          className="mt-6"
          {...register('email')}
        />
        {errors.email && (
          <p className="text-red-500 text-[10px] mt-1">
            {errors.email.message}
          </p>
        )}

        <InputLabel
          type="email"
          label="Confirm Email"
          placeholder="Confirm your email"
          className="mt-6"
          {...register('confirmEmail')}
        />
        {errors.confirmEmail && (
          <p className="text-red-500 text-[10px] mt-1">
            {errors.confirmEmail.message}
          </p>
        )}

        <InputLabel
          type="password"
          label="Password"
          placeholder="Password"
          className="mt-6"
          {...register('password')}
        />
        {errors.password && (
          <p className="text-red-500 text-[10px] mt-1">
            {errors.password.message}
          </p>
        )}

        <Button
          type="submit"
          className="mt-6 bg-gradient-to-r text-white text-xs bg-[#2ec0df] font-medium hover:bg-[#2593ac]"
        >
          Register
        </Button>

        <div className="flex items-center gap-2 text-gray-400 text-xs mt-6">
          <div className="flex-1 border-t border-gray-200" />
          <span>or continue with</span>
          <div className="flex-1 border-t border-gray-200" />
        </div>

        <div className="flex flex-col sm:flex-row gap-2 mt-6">
          <ButtonWithIcon
            icon={<FaGoogle className="mr-2" />}
            text="Register with Google"
          />
          <ButtonWithIcon
            icon={<FaApple className="mr-2" />}
            text="Register with Apple"
          />
        </div>

        <div className="flex justify-center items-center text-xs gap-1 mt-2 flex-wrap">
          <span>Already have an account?</span>
          <h1
            onClick={() => navigate('/auth/login')}
            className="text-xs text-[#2ec0df] font-semibold cursor-pointer hover:underline"
          >
            Login here
          </h1>
        </div>
      </div>
    </form>
  );
};
