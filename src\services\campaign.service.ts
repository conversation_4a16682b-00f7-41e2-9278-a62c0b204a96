import { apiClient } from './api';
import type {
  CampaignSetupRequest,
  CampaignResponse,
  ApiResponse,
  Campaign,
  PaginatedResponse,
} from '@/types';

export class CampaignService {
  private static readonly ENDPOINT = '/campaign-setup';

  static async createCampaign(campaignData: CampaignSetupRequest): Promise<CampaignResponse> {
    try {
      const apiData: CampaignSetupRequest = campaignData;

      const response = await apiClient.post<ApiResponse<CampaignResponse>>(
        this.ENDPOINT,
        apiData
      );
      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to create campaign');
      }
      return response.data.data;
    } catch (error) {
      console.error('Erro ao criar campanha:', error);
      throw error;
    }
  }

  static async getCampaignById(id: string): Promise<Campaign> {
    try {
      const response = await apiClient.get<ApiResponse<Campaign>>(
        `${this.ENDPOINT}/${id}`
      );
      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to fetch campaign');
      }
      return response.data.data;
    } catch (error) {
      console.error(`Erro ao buscar campanha com ID ${id}:`, error);
      throw error;
    }
  }

  static async getAllCampaigns(
    page = 1,
    limit = 10
  ): Promise<PaginatedResponse<Campaign>> {
    try {
      const response = await apiClient.get<
        ApiResponse<PaginatedResponse<Campaign>>
      >(`${this.ENDPOINT}?page=${page}&limit=${limit}`);
      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to fetch campaigns');
      }
      return response.data.data;
    } catch (error) {
      console.error('Erro ao buscar campanhas:', error);
      throw error;
    }
  }
}

export default CampaignService;
