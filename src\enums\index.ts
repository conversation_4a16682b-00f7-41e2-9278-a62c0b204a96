// Export values
import { CampaignConscienceLevel } from './campaignConscienceLevel.enum';
import { CampaignSophisticationLevel } from './campaignSophisticationLevel.enum';
import { CampaignType } from './campaignType.enum';
import { ProductCustomerJourney } from './productCustomerJourney.enum';
import { ProductDeliverables } from './productDeliverables.enum';
import { PublicEducationLevel } from './publicEducationLevel.enum';
import { PublicGender } from './publicGender.enum';
import { PublicAgeGroup } from './publicAgeGroup.enum';

// Export types
export type { CampaignConscienceLevelEnum } from './campaignConscienceLevel.enum';
export type { CampaignSophisticationLevelEnum } from './campaignSophisticationLevel.enum';
export type { CampaignTypeEnum } from './campaignType.enum';
export type { ProductCustomerJourneyEnum } from './productCustomerJourney.enum';
export type { ProductDeliverablesEnum } from './productDeliverables.enum';
export type { PublicEducationLevelEnum } from './publicEducationLevel.enum';
export type { PublicGenderEnum } from './publicGender.enum';
export type { PublicAgeGroupEnum } from './publicAgeGroup.enum';

// Export values
export {
  CampaignConscienceLevel,
  CampaignSophisticationLevel,
  CampaignType,
  ProductCustomerJourney,
  ProductDeliverables,
  PublicEducationLevel,
  PublicGender,
  PublicAgeGroup
};
