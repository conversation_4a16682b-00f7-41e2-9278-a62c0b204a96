import { useEffect, useState } from 'react';
import { Button } from '@/components/atoms/button';
import { Input } from '@/components/atoms/input';
import { Label } from '@/components/atoms/label';
import { Textarea } from '@/components/atoms/textarea';
import {
  ProductService,
  type ProductSetupRequest,
} from '@/services/product.service';
import { toast } from '@/components/atoms/toaster';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/atoms/dialog';

type ProductFormDialogProps = {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  productId?: string | null;
  onSuccess?: () => void;
};

type ProductFormData = Omit<ProductSetupRequest, 'price'> & {
  price: number | string;
};

export function ProductFormDialog({
  open,
  onOpenChange,
  productId,
  onSuccess,
}: ProductFormDialogProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState<
    Omit<ProductFormData, 'description'> & { description: string }
  >({
    name: '',
    description: '',
    price: '',
  });

  useEffect(() => {
    const loadProduct = async () => {
      if (!productId) {
        setFormData({ name: '', description: '', price: '' });
        return;
      }

      try {
        setIsLoading(true);
        const product = await ProductService.getProductById(productId);
        setFormData({
          name: product.name,
          description: product.description || '',
          price: product.price?.toString() || '',
        });
      } catch (error) {
        console.error('Error loading product:', error);
        toast({
          title: 'Erro',
          description: 'Não foi possível carregar os dados do produto',
          variant: 'destructive',
        });
        onOpenChange(false);
      } finally {
        setIsLoading(false);
      }
    };

    if (open) {
      loadProduct();
    }
  }, [productId, open, onOpenChange]);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === 'price' ? value : value,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast({
        title: 'Erro',
        description: 'O nome do produto é obrigatório',
        variant: 'destructive',
      });
      return;
    }

    try {
      setIsLoading(true);

      const productData: ProductSetupRequest = {
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        price: formData.price ? Number(formData.price) : undefined,
      };

      if (productId) {
        await ProductService.updateProduct(productId, productData);
        toast({
          title: 'Sucesso',
          description: 'Produto atualizado com sucesso!',
        });
      } else {
        await ProductService.createProduct(productData);
        toast({
          title: 'Sucesso',
          description: 'Produto criado com sucesso!',
        });
      }

      onSuccess?.();
      onOpenChange(false);
    } catch (error) {
      console.error('Error saving product:', error);
      toast({
        title: 'Erro',
        description: 'Ocorreu um erro ao salvar o produto',
        variant: 'destructive',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <form onSubmit={handleSubmit}>
          <DialogHeader>
            <DialogTitle>
              {productId ? 'Editar Produto' : 'Novo Produto'}
            </DialogTitle>
            <DialogDescription>
              {productId
                ? 'Atualize as informações do produto'
                : 'Preencha os dados para adicionar um novo produto'}
            </DialogDescription>
          </DialogHeader>

          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="name" className="text-right">
                Nome *
              </Label>
              <Input
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                className="col-span-3"
                disabled={isLoading}
                required
              />
            </div>

            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="price" className="text-right">
                Preço
              </Label>
              <Input
                id="price"
                name="price"
                type="number"
                step="0.01"
                min="0"
                value={formData.price}
                onChange={handleChange}
                className="col-span-3"
                disabled={isLoading}
              />
            </div>

            <div className="grid grid-cols-4 gap-4">
              <Label htmlFor="description" className="text-right pt-2">
                Descrição
              </Label>
              <Textarea
                id="description"
                name="description"
                value={formData.description}
                onChange={handleChange}
                className="col-span-3"
                rows={4}
                disabled={isLoading}
              />
            </div>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isLoading}
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading ? 'Salvando...' : 'Salvar'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
