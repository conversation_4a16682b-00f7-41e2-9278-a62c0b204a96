import { Button } from '@/components/atoms/button';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/atoms/card';
import { Plus } from 'lucide-react';
import { useEffect, useState } from 'react';
import { Skeleton } from '@/components/atoms/skeleton';

type Entity = {
  id: string;
  name: string;
  description?: string;
  createdAt?: string;
};

type EntityListProps = {
  title: string;
  fetchEntities: () => Promise<Entity[]>;
  onSelect: (id: string) => void;
  onCreateNew: () => void;
  emptyMessage?: string;
  className?: string;
};

export function EntityList({
  title,
  fetchEntities,
  onSelect,
  onCreateNew,
  emptyMessage = 'Nenhum item encontrado',
  className = '',
}: EntityListProps) {
  const [entities, setEntities] = useState<Entity[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadEntities = async () => {
      try {
        setIsLoading(true);
        const data = await fetchEntities();
        setEntities(data);
        setError(null);
      } catch (err) {
        console.error(`Error loading ${title.toLowerCase()}:`, err);
        setError(`Erro ao carregar ${title.toLowerCase()}`);
      } finally {
        setIsLoading(false);
      }
    };

    loadEntities();
  }, [fetchEntities, title]);

  if (isLoading) {
    return (
      <Card className={`${className} h-full`}>
        <CardHeader>
          <CardTitle className="text-lg">{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} className="h-16 w-full" />
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`${className} h-full`}>
        <CardHeader>
          <CardTitle className="text-lg">{title}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-destructive text-center py-4">{error}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={`${className} h-full`}>
      <CardHeader className="flex flex-row items-center justify-between pb-2">
        <CardTitle className="text-lg">{title}</CardTitle>
        <Button
          variant="ghost"
          size="icon"
          onClick={onCreateNew}
          title="Adicionar novo"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </CardHeader>
      <CardContent className="space-y-2 max-h-[calc(100vh-200px)] overflow-y-auto">
        {entities.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            {emptyMessage}
          </div>
        ) : (
          entities.map((entity) => (
            <div
              key={entity.id}
              onClick={() => onSelect(entity.id)}
              className="p-3 rounded-lg border hover:bg-accent hover:text-accent-foreground cursor-pointer transition-colors"
            >
              <h4 className="font-medium">{entity.name}</h4>
              {entity.description && (
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {entity.description}
                </p>
              )}
              {entity.createdAt && (
                <p className="text-xs text-muted-foreground mt-1">
                  Criado em: {new Date(entity.createdAt).toLocaleDateString()}
                </p>
              )}
            </div>
          ))
        )}
      </CardContent>
    </Card>
  );
}
