import { apiClient } from './api';
import type { ExpertSetupRequest, Expert, ApiResponse } from '../types';

export class ExpertService {
  private static readonly ENDPOINT = '/expert-setup';

  static async createExpert(expertData: ExpertSetupRequest) {
    try {
      const response = await apiClient.post(this.ENDPOINT, expertData);
      return response.data;
    } catch (error) {
      console.error('Erro ao criar expert:', error);
      throw error;
    }
  }

  static async getAllExperts(): Promise<Expert[]> {
    try {
      const response = await apiClient.get<ApiResponse<Expert[]>>(
        this.ENDPOINT
      );
      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to fetch experts');
      }
      return response.data.data;
    } catch (error) {
      console.error('Erro ao buscar especialistas:', error);
      throw error;
    }
  }

  static async getExpertById(id: string): Promise<Expert> {
    try {
      const response = await apiClient.get<Expert>(`${this.ENDPOINT}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Erro ao buscar expert com ID ${id}:`, error);
      throw error;
    }
  }
}

export default ExpertService;
