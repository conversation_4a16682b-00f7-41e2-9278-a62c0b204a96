import React, {
  createContext,
  useContext,
  useState,
  type ReactNode,
  useCallback,
} from 'react';
import type { CampaignSetupRequest, CampaignResponse } from '@/types';
import { CampaignService } from '../services/campaign.service';

type FormWizardContextType = {
  currentStep: number;
  totalSteps: number;
  campaignData: Partial<CampaignSetupRequest>;
  generatedCopy: string | null;
  isLoading: boolean;
  error: string | null;
  nextStep: () => void;
  prevStep: () => void;
  goToStep: (step: number) => void;
  updateCampaignData: (data: Partial<CampaignSetupRequest>) => void;
  generateCopy: (instructions: string) => Promise<CampaignResponse>;
  resetForm: () => void;
};

const FormWizardContext = createContext<FormWizardContextType | undefined>(
  undefined
);

export const FormWizardProvider: React.FC<{ children: ReactNode }> = ({
  children,
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [campaignData, setCampaignData] = useState<
    Partial<CampaignSetupRequest>
  >({});
  const [generatedCopy, setGeneratedCopy] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const totalSteps = 5;

  const nextStep = () =>
    setCurrentStep((prev) => Math.min(prev + 1, totalSteps));
  const prevStep = () => setCurrentStep((prev) => Math.max(prev - 1, 1));
  const goToStep = (step: number) =>
    setCurrentStep(Math.max(1, Math.min(step, totalSteps)));

  const updateCampaignData = useCallback(
    (data: Partial<CampaignSetupRequest>) => {
      setCampaignData((prev) => ({
        ...prev,
        ...data,
      }));
    },
    []
  );

  const resetForm = useCallback(() => {
    setCurrentStep(1);
    setCampaignData({});
    setGeneratedCopy(null);
    setError(null);
  }, []);

  const generateCopy = async (
    instructions: string
  ): Promise<CampaignResponse> => {
    setIsLoading(true);
    setError(null);

    try {
      const dataToSend = {
        ...campaignData,
        instructions,
      } as CampaignSetupRequest;

      const result = await CampaignService.createCampaign(dataToSend);
      const generatedText = result.copy || 'Copy gerada com sucesso!';
      setGeneratedCopy(generatedText);
      return { ...result, copy: generatedText };
    } catch (err) {
      console.error('Error generating copy:', err);
      const errorMessage = 'Erro ao gerar o copy. Por favor, tente novamente.';
      setError(errorMessage);
      throw new Error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <FormWizardContext.Provider
      value={{
        currentStep,
        totalSteps,
        campaignData,
        generatedCopy,
        isLoading,
        error,
        nextStep,
        prevStep,
        goToStep,
        updateCampaignData,
        generateCopy,
        resetForm,
      }}
    >
      {children}
    </FormWizardContext.Provider>
  );
};

export const useFormWizard = () => {
  const context = useContext(FormWizardContext);
  if (context === undefined) {
    throw new Error('useFormWizard must be used within a FormWizardProvider');
  }
  return context;
};
