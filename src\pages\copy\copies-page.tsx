import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { MessageService, type Copy } from '@/services/message.service';
import { Card, CardContent } from '@/components/atoms/card';
import { Skeleton } from '@/components/atoms/skeleton';
import { Button } from '@/components/atoms/button';
import { FileText, Plus } from 'lucide-react';

export function CopiesPage() {
  const [copies, setCopies] = useState<Copy[]>([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    const fetchCopies = async () => {
      try {
        const data = await MessageService.getAll();
        const formattedCopies = data.map(item => ({
          ...item,
          title: item.title || `Copy ${item.id}`,
          content: item.content || item.aiResponse || item.userPrompt || '',
          createdAt: item.createdAt || new Date().toISOString()
        }));
        
        setCopies(formattedCopies);
      } catch (error) {
        console.error('Error fetching copies:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchCopies();
  }, []);

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return '';
    try {
      return new Date(dateString).toLocaleDateString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return '';
    }
  };

  const truncateText = (text: string | undefined, maxLength = 100) => {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return `${text.substring(0, maxLength)}...`;
  };

  if (loading) {
    return (
      <div className="container mx-auto p-4">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Minhas Copies</h1>
          <Button>
            <Plus className="h-4 w-4 mr-2" />
            Nova Copy
          </Button>
        </div>
        <div className="grid gap-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="p-4">
              <Skeleton className="h-6 w-1/3 mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-1/2" />
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-4">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Minhas Copies</h1>
        <Button onClick={() => navigate('/app/generate-copy')}>
          <Plus className="h-4 w-4 mr-2" />
          Nova Copy
        </Button>
      </div>

      {copies.length === 0 ? (
        <div className="text-center py-12 border rounded-lg">
          <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900">Nenhuma copy encontrada</h3>
          <p className="mt-1 text-gray-500">Crie sua primeira copy clicando no botão acima.</p>
        </div>
      ) : (
        <div className="grid gap-4">
          {copies.map((copy) => (
            <Card 
              key={copy.id}
              className="cursor-pointer hover:bg-gray-50 transition-colors"
              onClick={() => navigate(`/app/copy/${copy.id}`)}
            >
              <CardContent className="p-4">
                <div className="flex justify-between items-start">
                  <h3 className="font-medium text-lg">{copy.title}</h3>
                  <span className="text-sm text-gray-500">
                    {formatDate(copy.createdAt)}
                  </span>
                </div>
                <p className="mt-2 text-gray-600">
                  {truncateText(copy.content)}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
}

export default CopiesPage;
