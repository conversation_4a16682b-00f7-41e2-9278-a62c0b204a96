import DashboardPage from '@/pages/dashboard-page';
import { Navigate, type RouteObject } from 'react-router-dom';
import GenerateContentPage from '@/pages/content/generate-content-page';
import AnalyzeFilePage from '@/pages/content/analyze-file-page';
import ProductsPage from '@/pages/products/products-page';
import ExpertsPage from '@/pages/experts/experts-page';
import CampaignsPage from '@/pages/campaigns/campaigns-page';
import PublicPage from '@/pages/public/public-page';

import GenerateCopyPage from '@/pages/copy/generate-copy-page';
import CopiesPage from '@/pages/copy/copies-page';
import CopyPage from '@/pages/copy/copy-page';
import AnalyzeWebsitePage from '@/pages/content/analyze-website-page';

export const appRoutes: RouteObject[] = [
  {
    path: '',
    element: <Navigate to="dashboard" replace />,
  },
  {
    path: 'dashboard',
    element: <DashboardPage />,
  },
  {
    path: 'experts',
    element: <ExpertsPage />,
  },
  {
    path: 'products',
    element: <ProductsPage />,
  },
  {
    path: 'generate-content',
    element: <GenerateContentPage />,
  },
  {
    path: 'analyze-website',
    element: <AnalyzeWebsitePage />,
  },
  {
    path: 'analyze-file',
    element: <AnalyzeFilePage />,
  },
  {
    path: 'public',
    element: <PublicPage />,
  },
  {
    path: 'copies',
    element: <CopiesPage />,
  },
  {
    path: 'copy/:id',
    element: <CopyPage />,
  },
  {
    path: 'generate-copy',
    element: <GenerateCopyPage />,
  },
  {
    path: 'campaigns',
    element: <CampaignsPage />,
    children: [
      {
        path: 'new',
        element: <Navigate to="/app/campaigns" replace />,
      },
    ],
  },
  {
    path: 'analytics',
    element: <div className="p-8">Analytics Dashboard - Coming Soon</div>,
  },
];
