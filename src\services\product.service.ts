import { apiClient } from './api';
import type { ApiResponse } from '@/types';
import { toast } from '@/components/atoms/toaster';

export interface Product {
  id: string;
  name: string;
  description?: string;
  price?: number;
  createdAt?: string;
  updatedAt?: string;
}

export interface ProductSetupRequest {
  name: string;
  description?: string;
  price?: number;
}

export class ProductService {
  private static readonly ENDPOINT = '/product-setup';

  static async createProduct(
    productData: ProductSetupRequest
  ): Promise<Partial<Product> | null> {
    try {
      const response = await apiClient.post<ApiResponse<Product>>(
        this.ENDPOINT,
        productData
      );
      if (!response.data.success || !response.data.data) {
        console.warn('Failed to create product:', response.data.error);
        return null;
      }
      toast({
        title: 'Sucesso',
        description: 'Produto criado com sucesso!',
        variant: 'default',
      });
      return response.data.data;
    } catch (error) {
      console.warn('Erro ao criar produto:', error);
      // Don't show error toast here, let the component handle it
      return null;
    }
  }

  static async updateProduct(
    id: string,
    productData: Partial<ProductSetupRequest>
  ): Promise<Product> {
    try {
      const response = await apiClient.put<ApiResponse<Product>>(
        `${this.ENDPOINT}/${id}`,
        productData
      );
      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to update product');
      }
      toast({
        title: 'Sucesso',
        description: 'Produto atualizado com sucesso!',
        variant: 'default',
      });
      return response.data.data;
    } catch (error) {
      console.error(`Erro ao atualizar produto com ID ${id}:`, error);
      toast({
        title: 'Erro',
        description: 'Não foi possível atualizar o produto. Tente novamente.',
        variant: 'destructive',
      });
      throw error;
    }
  }

  static async getProductById(id: string): Promise<Product> {
    try {
      const response = await apiClient.get<ApiResponse<Product>>(
        `${this.ENDPOINT}/${id}`
      );
      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to fetch product');
      }
      return response.data.data;
    } catch (error) {
      console.error(`Erro ao buscar produto com ID ${id}:`, error);
      throw error;
    }
  }

  static async getAllProducts(): Promise<Product[]> {
    try {
      const response = await apiClient.get<ApiResponse<Product[]>>(
        this.ENDPOINT
      );
      if (!response.data.success || !response.data.data) {
        throw new Error(response.data.error || 'Failed to fetch products');
      }
      return response.data.data;
    } catch (error) {
      console.error('Erro ao buscar produtos:', error);
      throw error;
    }
  }

  static async deleteProduct(id: string): Promise<void> {
    try {
      const response = await apiClient.delete<ApiResponse<void>>(
        `${this.ENDPOINT}/${id}`
      );
      if (!response.data.success) {
        throw new Error(response.data.error || 'Failed to delete product');
      }
      toast({
        title: 'Sucesso',
        description: 'Produto excluído com sucesso!',
        variant: 'default',
      });
    } catch (error) {
      console.error(`Erro ao deletar produto com ID ${id}:`, error);
      toast({
        title: 'Erro',
        description: 'Não foi possível excluir o produto. Tente novamente.',
        variant: 'destructive',
      });
      throw error;
    }
  }
}

export default ProductService;
