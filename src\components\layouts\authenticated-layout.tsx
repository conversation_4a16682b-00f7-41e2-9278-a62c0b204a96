import { Outlet, useLocation, useNavigate } from 'react-router-dom';
import { Sidebar } from '../organisms';
import { SidebarItem } from '../organisms/sidebar';
import {
  BarChart3,
  GitGraph,
  Users,
  Package,
  Megaphone,
  FileText,
  User,
  Megaphone as MegaphoneIcon,
  ClipboardList,
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface AuthenticatedLayoutProps {
  className?: string;
}

export function AuthenticatedLayout({ className }: AuthenticatedLayoutProps) {
  const location = useLocation();
  const navigate = useNavigate();
  const isActive = (path: string) => location.pathname.includes(path);
  const isFormStep = location.pathname.includes('/app/campaigns/new');
  const currentStep = isFormStep
    ? parseInt(location.pathname.split('/').pop() || '1')
    : 0;

  const formSteps = [
    {
      number: 1,
      label: 'Informações da Empresa',
      path: '/app/campaigns/new/1',
    },
    { number: 2, label: 'Público-Alvo', path: '/app/campaigns/new/2' },
    { number: 3, label: 'Detalhes do Produto', path: '/app/campaigns/new/3' },
    {
      number: 4,
      label: 'Configuração da Campanha',
      path: '/app/campaigns/new/4',
    },
    {
      number: 5,
      label: 'Detalhes do Especialista',
      path: '/app/campaigns/new/5',
    },
  ];

  return (
    <div className={cn('w-full min-h-screen', className)}>
      <main className="w-full flex">
        <Sidebar>
          {isFormStep ? (
            <>
              <div className="px-4 py-2 text-sm font-medium text-muted-foreground">
                Criar Nova Campanha
              </div>
              {formSteps.map((step) => (
                <SidebarItem
                  key={step.number}
                  icon={getStepIcon(step.number)}
                  text={`${step.number}. ${step.label}`}
                  active={currentStep === step.number}
                  onClick={() => navigate(step.path)}
                  className={currentStep === step.number ? 'bg-accent' : ''}
                />
              ))}
              <div className="mt-4 pt-4 border-t">
                <SidebarItem
                  icon={<BarChart3 size={20} />}
                  text="Voltar ao Dashboard"
                  href="/app/dashboard"
                />
              </div>
            </>
          ) : (
            <>
              <SidebarItem
                icon={<BarChart3 size={20} />}
                text="Dashboard"
                active={isActive('/dashboard')}
                href="/app/dashboard"
              />
              <SidebarItem
                icon={<Users size={20} />}
                text="Especialistas"
                active={isActive('/experts')}
                href="/app/experts"
              />
              <SidebarItem
                icon={<Package size={20} />}
                text="Produtos"
                active={isActive('/products')}
                href="/app/products"
              />
              <SidebarItem
                icon={<Megaphone size={20} />}
                text="Campanhas"
                active={isActive('/campaigns') && !isFormStep}
                href="/app/campaigns"
              />
              <SidebarItem
                icon={<Users size={20} />}
                text="Público"
                active={isActive('/public')}
                href="/app/public"
              />
              <SidebarItem
                icon={<ClipboardList size={20} />}
                text="Minhas Copys"
                active={isActive('/copies')}
                href="/app/copies"
              />
              <SidebarItem
                icon={<GitGraph size={20} />}
                text="Estatísticas"
                active={isActive('/analytics')}
                href="/app/analytics"
              />
            </>
          )}
        </Sidebar>
        <div className="py-16 px-8 w-full h-[500px]">
          <Outlet />
        </div>
      </main>
    </div>
  );
}

function getStepIcon(stepNumber: number) {
  switch (stepNumber) {
    case 1:
      return <FileText size={18} />;
    case 2:
      return <Users size={18} />;
    case 3:
      return <Package size={18} />;
    case 4:
      return <MegaphoneIcon size={18} />;
    case 5:
      return <User size={18} />;
    default:
      return <FileText size={18} />;
  }
}
