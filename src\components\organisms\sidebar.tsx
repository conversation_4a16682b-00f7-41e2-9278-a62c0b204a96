import {
  ChevronFirst,
  ChevronLast,
  Moon,
  MoreVertical,
  Sun,
  Sparkles,
  Globe,
  FileText,
  ClipboardEdit,
} from 'lucide-react';
import logo from '../../assests/logo.png';
import icon from '../../assests/icon.png';
import { createContext, useContext, useState } from 'react';
import {
  Button,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from '../atoms';
import {
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuSeparator,
} from '../atoms/dropdown-menu';
import { useTheme } from '@/hooks/use-theme';
import { useNavigate } from 'react-router-dom';

type SidebarProps = {
  children: React.ReactNode;
};
export const SidebarContext = createContext({ expanded: true });

export const Sidebar = ({ children }: SidebarProps) => {
  const [expanded, setExpanded] = useState(true);
  const navigate = useNavigate();

  const handleChangeExpanded = () => {
    setExpanded((prev) => !prev);
  };

  return (
    <aside
      className={`h-screen ${expanded ? 'w-70' : 'w-18'}`}
    >
      <nav className="h-full flex flex-col shadow-sm">
        <div className="p-4 pb-2 flex flex-col gap-4">
          <div className="flex justify-between items-center">
            {expanded ? (
              <img
                src={logo}
                className={`overflow-hidden transition-all ${expanded ? 'w-32' : 'w-0'}`}
                alt=""
              />
            ) : (
              <img src={icon} alt="" />
            )}
            <Button
              variant="outline"
              className={`${expanded ? '' : 'transition-all w-2 h-2'}`}
              onClick={handleChangeExpanded}
            >
              {expanded ? <ChevronFirst /> : <ChevronLast />}
            </Button>
          </div>
        </div>
        <SidebarContext.Provider value={{ expanded }}>
          <ul className="px-3 mt-6">
            {children}
            {expanded && (
              <li className="px-3 py-2 text-xs font-medium text-gray-500 mt-8 mb-2">
                Gerenciamento
              </li>
            )}

            <li
              className="flex items-center p-2 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200"
              onClick={() => navigate('/app/generate-content')}
              title={!expanded ? 'Gerar Conteúdo' : ''}
            >
              <Sparkles className="w-5 h-5" />
              {expanded && <span className="ml-3">Gerar Conteúdo</span>}
            </li>

            <li
              className="flex items-center p-2 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200"
              onClick={() => navigate('/app/analyze-website')}
              title={!expanded ? 'Analisar Website' : ''}
            >
              <Globe className="w-5 h-5" />
              {expanded && <span className="ml-3">Analisar Website</span>}
            </li>

            <li
              className="flex items-center p-2 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200"
              onClick={() => navigate('/app/analyze-file')}
              title={!expanded ? 'Analisar Arquivo' : ''}
            >
              <FileText className="w-5 h-5" />
              {expanded && <span className="ml-3">Analisar Arquivo</span>}
            </li>

            <li
              className="flex items-center p-2 rounded-lg cursor-pointer hover:bg-gray-100 dark:hover:bg-gray-700 text-gray-700 dark:text-gray-200"
              onClick={() => navigate('/app/generate-copy')}
              title={!expanded ? 'Gerar Copy' : ''}
            >
              <ClipboardEdit className="w-5 h-5" />
              {expanded && <span className="ml-3">Gerar Copy</span>}
            </li>
          </ul>
        </SidebarContext.Provider>
        <div className="border-t border-b py-3">
          <div className="flex items-center">
            <img
              src="https://plus.unsplash.com/premium_photo-1689708721750-8a0e6dc14cee?fm=jpg&q=60&w=3000&ixlib=rb-4.1.0&ixid=M3wxMjA3fDB8MHxzZWFyY2h8OXx8bWFuJTIwYXZhdGFyfGVufDB8fDB8fHww"
              className="w-10 h-10 rounded-md object-cover"
              alt=""
            />
            <div
              className={`flex justify-between items-center overflow-hidden transition-all ${expanded ? 'w-52 ml-3' : 'w-0'}`}
            >
              <div className="leading-4">
                <h4 className="font-semibold">John Doe</h4>
                <p className="text-xs text-gray-600"><EMAIL></p>
              </div>
              <SidebarUserMenu />
            </div>
          </div>
        </div>
      </nav>
    </aside>
  );
};

type SidebarItemProps = {
  icon: React.ReactNode;
  text: string;
  active?: boolean;
  alert?: boolean;
  href?: string;
  onClick?: () => void;
  className?: string;
};

export const SidebarItem = ({
  icon,
  text,
  active,
  alert,
  href = '#',
  onClick,
  className = '',
}: SidebarItemProps) => {
  const { expanded } = useContext(SidebarContext);

  const content = (
    <>
      {icon}
      {expanded && (
        <span
          className={`overflow-hidden transition-all ${expanded ? 'w-52 ml-3' : 'w-0'}`}
        >
          {text}
        </span>
      )}
      {alert && (
        <div
          className={`absolute right-2 w-2 h-2 rounded bg-red-400 ${expanded ? '' : 'top-2'}`}
        />
      )}
      {!expanded && (
        <div
          className={`
            absolute left-full rounded-md px-2 py-1 ml-6 text-sm
            invisible opacity-20 -translate-x-3 transition-all
            group-hover:visible group-hover:opacity-100 group-hover:translate-x-0
            bg-gray-800 text-white
          `}
        >
          {text}
        </div>
      )}
    </>
  );

  const commonProps = {
    className: `relative flex py-3 px-3 my-1
               font-medium rounded-md cursor-pointer
               transition-colors group hover:bg-gray-100
               ${active ? 'bg-gray-100' : ''} ${className}`,
    onClick,
  };

  return (
    <li>
      {onClick ? (
        <div {...commonProps}>{content}</div>
      ) : (
        <a href={href} {...commonProps}>
          {content}
        </a>
      )}
    </li>
  );
};

export const SidebarUserMenu = () => {
  const { setTheme, theme } = useTheme();
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost">
          <MoreVertical size={20} />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56 ml-10 mb-[-40px]" align="start">
        <DropdownMenuLabel>My Account</DropdownMenuLabel>
        <DropdownMenuGroup>
          <DropdownMenuItem>Profile</DropdownMenuItem>
          <DropdownMenuItem>Billing</DropdownMenuItem>
          <DropdownMenuItem>Settings</DropdownMenuItem>
        </DropdownMenuGroup>
        <DropdownMenuSeparator />
        <DropdownMenuItem
          onClick={() => setTheme(theme == 'light' ? 'dark' : 'light')}
        >
          Theme:{' '}
          {theme == 'light' ? (
            <>
              <Sun />
              Light
            </>
          ) : (
            <>
              <Moon />
              Dark
            </>
          )}
        </DropdownMenuItem>
        <DropdownMenuItem>Log out</DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
